{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../components/Hero';\n\nexport default function DashboardHome() {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      welcome: 'Welcome to DTC Accelerator',\n      adminDashboard: 'Admin Dashboard',\n      consultantDashboard: 'Consultant Dashboard',\n      projectManagerDashboard: 'Project Manager Dashboard',\n      traineeDashboard: 'Trainee Dashboard',\n      overview: 'Dashboard Overview',\n      description: 'This is your personalized dashboard based on your role and permissions.',\n      quickStats: 'Quick Statistics',\n      recentActivity: 'Recent Activity'\n    },\n    ar: {\n      welcome: 'مرحباً بك في مسرع التحول الرقمي',\n      adminDashboard: 'لوحة تحكم المدير',\n      consultantDashboard: 'لوحة تحكم المستشار',\n      projectManagerDashboard: 'لوحة تحكم مدير المشروع',\n      traineeDashboard: 'لوحة تحكم المتدرب',\n      overview: 'نظرة عامة على لوحة التحكم',\n      description: 'هذه لوحة التحكم الشخصية الخاصة بك بناءً على دورك وصلاحياتك.',\n      quickStats: 'إحصائيات سريعة',\n      recentActivity: 'النشاط الأخير'\n    }\n  };\n\n  const getDashboardTitle = () => {\n    switch (userRole) {\n      case 'admin':\n        return content[language].adminDashboard;\n      case 'consultant':\n        return content[language].consultantDashboard;\n      case 'project-manager':\n        return content[language].projectManagerDashboard;\n      case 'trainee':\n        return content[language].traineeDashboard;\n      default:\n        return content[language].adminDashboard;\n    }\n  };\n\n  const getStatsForRole = () => {\n    switch (userRole) {\n      case 'admin':\n        return [\n          { label: language === 'en' ? 'Total Users' : 'إجمالي المستخدمين', value: '1,234', icon: '👥' },\n          { label: language === 'en' ? 'Active Projects' : 'المشاريع النشطة', value: '56', icon: '📊' },\n          { label: language === 'en' ? 'Frameworks' : 'الأطر', value: '12', icon: '🏗️' },\n          { label: language === 'en' ? 'Training Courses' : 'الدورات التدريبية', value: '89', icon: '🎓' }\n        ];\n      case 'consultant':\n        return [\n          { label: language === 'en' ? 'My Projects' : 'مشاريعي', value: '8', icon: '📊' },\n          { label: language === 'en' ? 'Consultations' : 'الاستشارات', value: '24', icon: '💼' },\n          { label: language === 'en' ? 'Completed Tasks' : 'المهام المكتملة', value: '156', icon: '✅' }\n        ];\n      case 'project-manager':\n        return [\n          { label: language === 'en' ? 'Managed Projects' : 'المشاريع المدارة', value: '12', icon: '📊' },\n          { label: language === 'en' ? 'Team Members' : 'أعضاء الفريق', value: '45', icon: '👥' },\n          { label: language === 'en' ? 'Milestones' : 'المعالم', value: '78', icon: '🎯' }\n        ];\n      case 'trainee':\n        return [\n          { label: language === 'en' ? 'Enrolled Courses' : 'الدورات المسجلة', value: '6', icon: '🎓' },\n          { label: language === 'en' ? 'Completed Modules' : 'الوحدات المكتملة', value: '23', icon: '✅' },\n          { label: language === 'en' ? 'Certificates' : 'الشهادات', value: '3', icon: '🏆' }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  const getHeroIcon = () => {\n    switch (userRole) {\n      case 'admin':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n          </svg>\n        );\n      case 'consultant':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6\" />\n          </svg>\n        );\n      case 'project-manager':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n          </svg>\n        );\n      case 'trainee':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n          </svg>\n        );\n      default:\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n          </svg>\n        );\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Hero Section */}\n      <Hero\n        title={content[language].welcome}\n        subtitle={getDashboardTitle()}\n        description={content[language].description}\n        icon={getHeroIcon()}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم' },\n          { label: language === 'en' ? 'Home' : 'الرئيسية' }\n        ]}\n      />\n\n      {/* Main Dashboard Content - No Gaps, Seamless */}\n      <div className=\"bg-white\">\n        {/* Statistics Section */}\n        <div className=\"px-12 py-8\">\n          <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n            {content[language].quickStats}\n          </h3>\n\n          {/* Statistics Cards - Horizontal Layout */}\n          <div className={`flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'} flex-wrap gap-6`}>\n            {getStatsForRole().map((stat, index) => (\n              <div key={index} className=\"flex-1 min-w-[280px] bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 border border-gray-100 hover:shadow-xl transition-all duration-300\">\n                <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                  <div\n                    className=\"w-16 h-16 rounded-2xl flex items-center justify-center text-white shadow-lg\"\n                    style={{ backgroundColor: 'var(--emerald-green)' }}\n                  >\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                    </svg>\n                  </div>\n                  <div className={`${language === 'ar' ? 'mr-6 text-right' : 'ml-6 text-left'}`}>\n                    <p className={`text-4xl font-bold mb-1 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      {stat.value}\n                    </p>\n                    <p className={`text-sm text-gray-600 font-semibold uppercase tracking-wide ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {stat.label}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Divider */}\n        <div className=\"h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent mx-12\"></div>\n\n        {/* Recent Activity Section */}\n        <div className=\"px-12 py-8\">\n          <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n            {content[language].recentActivity}\n          </h3>\n\n          {/* Activity Items - Clean List */}\n          <div className=\"space-y-6\">\n            {[1, 2, 3, 4, 5].map((item) => (\n              <div key={item} className={`flex items-center p-6 bg-gradient-to-r from-gray-50 to-white rounded-2xl border border-gray-100 hover:shadow-lg transition-all duration-300 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div\n                  className=\"w-12 h-12 rounded-2xl flex items-center justify-center text-white shadow-lg\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <div className={`flex-1 ${language === 'ar' ? 'mr-6 text-right' : 'ml-6 text-left'}`}>\n                  <p className={`text-lg font-semibold mb-1 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {language === 'en' ? `Recent activity item ${item}` : `عنصر النشاط الأخير ${item}`}\n                  </p>\n                  <p className={`text-sm text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {language === 'en' ? '2 hours ago' : 'منذ ساعتين'}\n                  </p>\n                </div>\n                <div className={`${language === 'ar' ? 'ml-4' : 'mr-4'}`}>\n                  <div className=\"w-2 h-2 rounded-full\" style={{ backgroundColor: 'var(--emerald-green)' }}></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;YACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YAExE,YAAY;YACZ,YAAY;QACd;kCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;QACA,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YACzC,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAC9C,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,uBAAuB;YAClD,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,gBAAgB;YAC3C;gBACE,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;QAC3C;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAqB,OAAO;wBAAS,MAAM;oBAAK;oBAC7F;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAM,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAS,OAAO;wBAAM,MAAM;oBAAM;oBAC9E;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAqB,OAAO;wBAAM,MAAM;oBAAK;iBAChG;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAW,OAAO;wBAAK,MAAM;oBAAK;oBAC/E;wBAAE,OAAO,aAAa,OAAO,kBAAkB;wBAAc,OAAO;wBAAM,MAAM;oBAAK;oBACrF;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAO,MAAM;oBAAI;iBAC7F;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAoB,OAAO;wBAAM,MAAM;oBAAK;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAgB,OAAO;wBAAM,MAAM;oBAAK;oBACtF;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAW,OAAO;wBAAM,MAAM;oBAAK;iBAChF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAmB,OAAO;wBAAK,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,sBAAsB;wBAAoB,OAAO;wBAAM,MAAM;oBAAI;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAY,OAAO;wBAAK,MAAM;oBAAK;iBAClF;YACH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAG7E;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BAEpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO;gBAChC,UAAU;gBACV,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;oBAAc;oBACzD;wBAAE,OAAO,aAAa,OAAO,SAAS;oBAAW;iBAClD;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gCAAM,OAAO;oCAAE,OAAO;gCAAuB;0CACxH,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;0CAI/B,6LAAC;gCAAI,WAAW,AAAC,QAA2D,OAApD,aAAa,OAAO,qBAAqB,YAAW;0CACzE,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;wCAAgB,WAAU;kDACzB,cAAA,6LAAC;4CAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;8DAC5E,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAuB;8DAEjD,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;sEACzD,6LAAC;4DAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;4DAAM,OAAO;gEAAE,OAAO;4DAAuB;sEACvH,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAE,WAAW,AAAC,+DAAqG,OAAvC,aAAa,OAAO,gBAAgB;sEAC9G,KAAK,KAAK;;;;;;;;;;;;;;;;;;uCAfT;;;;;;;;;;;;;;;;kCAyBhB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gCAAM,OAAO;oCAAE,OAAO;gCAAuB;0CACxH,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;0CAInC,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;wCAAe,WAAW,AAAC,+IAAkM,OAApD,aAAa,OAAO,qBAAqB;;0DACjN,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAuB;0DAEjD,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,UAAkE,OAAzD,aAAa,OAAO,oBAAoB;;kEAChE,6LAAC;wDAAE,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAC1H,aAAa,OAAO,AAAC,wBAA4B,OAAL,QAAS,AAAC,sBAA0B,OAAL;;;;;;kEAE9E,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,aAAa,OAAO,gBAAgB;;;;;;;;;;;;0DAGzC,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;0DAC9C,cAAA,6LAAC;oDAAI,WAAU;oDAAuB,OAAO;wDAAE,iBAAiB;oDAAuB;;;;;;;;;;;;uCAlBjF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BxB;GA/MwB;KAAA", "debugId": null}}]}