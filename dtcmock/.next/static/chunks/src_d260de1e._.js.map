{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/DAMAWheel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface DAMAWheelProps {\n  language: 'en' | 'ar';\n  framework: 'ndmo' | 'npc';\n  onDomainClick: (domain: string) => void;\n}\n\nexport default function DAMAWheel({ language, framework, onDomainClick }: DAMAWheelProps) {\n  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);\n\n  const ndmoDomains = [\n    {\n      id: 'data-governance',\n      name: { en: 'Data Governance', ar: 'حوكمة البيانات' },\n      angle: 0,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-architecture',\n      name: { en: 'Data Architecture', ar: 'هندسة البيانات' },\n      angle: 45,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-modeling',\n      name: { en: 'Data Modeling', ar: 'نمذجة البيانات' },\n      angle: 90,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-storage',\n      name: { en: 'Data Storage', ar: 'تخزين البيانات' },\n      angle: 135,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-security',\n      name: { en: 'Data Security', ar: 'أمان البيانات' },\n      angle: 180,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-integration',\n      name: { en: 'Data Integration', ar: 'تكامل البيانات' },\n      angle: 225,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-quality',\n      name: { en: 'Data Quality', ar: 'جودة البيانات' },\n      angle: 270,\n      color: '#026c4a'\n    },\n    {\n      id: 'metadata',\n      name: { en: 'Metadata', ar: 'البيانات الوصفية' },\n      angle: 315,\n      color: '#0c402e'\n    }\n  ];\n\n  const npcDomains = [\n    {\n      id: 'strategic-planning',\n      name: { en: 'Strategic Planning', ar: 'التخطيط الاستراتيجي' },\n      angle: 0,\n      color: '#026c4a'\n    },\n    {\n      id: 'performance-management',\n      name: { en: 'Performance Management', ar: 'إدارة الأداء' },\n      angle: 45,\n      color: '#0c402e'\n    },\n    {\n      id: 'resource-allocation',\n      name: { en: 'Resource Allocation', ar: 'تخصيص الموارد' },\n      angle: 90,\n      color: '#026c4a'\n    },\n    {\n      id: 'policy-development',\n      name: { en: 'Policy Development', ar: 'تطوير السياسات' },\n      angle: 135,\n      color: '#0c402e'\n    },\n    {\n      id: 'monitoring-evaluation',\n      name: { en: 'Monitoring & Evaluation', ar: 'المراقبة والتقييم' },\n      angle: 180,\n      color: '#026c4a'\n    },\n    {\n      id: 'stakeholder-engagement',\n      name: { en: 'Stakeholder Engagement', ar: 'إشراك أصحاب المصلحة' },\n      angle: 225,\n      color: '#0c402e'\n    },\n    {\n      id: 'risk-management',\n      name: { en: 'Risk Management', ar: 'إدارة المخاطر' },\n      angle: 270,\n      color: '#026c4a'\n    },\n    {\n      id: 'innovation-development',\n      name: { en: 'Innovation Development', ar: 'تطوير الابتكار' },\n      angle: 315,\n      color: '#0c402e'\n    }\n  ];\n\n  const domains = framework === 'ndmo' ? ndmoDomains : npcDomains;\n\n  const handleDomainClick = (domain: any) => {\n    setSelectedDomain(domain.id);\n    onDomainClick(domain.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'DAMA Data Management Domains' : 'مجالات إدارة البيانات DAMA'}\n      </h3>\n\n      <div className=\"relative w-[500px] h-[500px] mx-auto\">\n        {/* Center Circle */}\n        <div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-2xl\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          <span className={language === 'ar' ? 'font-arabic' : ''}>\n            {language === 'en' ? 'DAMA' : 'داما'}\n          </span>\n        </div>\n\n        {/* Domain Segments */}\n        {domains.map((domain, index) => {\n          const radius = 180;\n          const centerX = 250;\n          const centerY = 250;\n          const angleRad = (domain.angle * Math.PI) / 180;\n          const x = centerX + radius * Math.cos(angleRad);\n          const y = centerY + radius * Math.sin(angleRad);\n\n          return (\n            <div key={domain.id} className=\"absolute inset-0 pointer-events-none\">\n              {/* Connection Line */}\n              <div\n                className=\"absolute top-1/2 left-1/2 origin-left h-0.5 opacity-30\"\n                style={{\n                  width: `${radius - 64}px`,\n                  backgroundColor: domain.color,\n                  transform: `translate(-50%, -50%) rotate(${domain.angle}deg)`,\n                  transformOrigin: 'left center'\n                }}\n              ></div>\n\n              {/* Domain Circle */}\n              <div\n                className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:scale-110 pointer-events-auto z-20 ${\n                  selectedDomain === domain.id ? 'scale-125' : ''\n                }`}\n                style={{\n                  left: x,\n                  top: y\n                }}\n                onClick={() => handleDomainClick(domain)}\n              >\n                <div\n                  className={`w-24 h-24 rounded-full flex items-center justify-center text-white font-semibold text-xs shadow-lg hover:shadow-2xl transition-all duration-300 ${\n                    selectedDomain === domain.id ? 'ring-4 ring-white' : ''\n                  }`}\n                  style={{ backgroundColor: domain.color }}\n                >\n                  <span className={`text-center leading-tight px-2 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {domain.name[language]}\n                  </span>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n\n        {/* Outer Ring */}\n        <div className=\"absolute inset-8 border-2 border-gray-200 rounded-full opacity-50\"></div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,UAAU,KAAsD;QAAtD,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAkB,GAAtD;;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAmB,IAAI;YAAiB;YACpD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAqB,IAAI;YAAiB;YACtD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAiB;YAClD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAiB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAgB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAoB,IAAI;YAAiB;YACrD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAgB;YAChD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAY,IAAI;YAAmB;YAC/C,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAsB,IAAI;YAAsB;YAC5D,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA0B,IAAI;YAAe;YACzD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAuB,IAAI;YAAgB;YACvD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAsB,IAAI;YAAiB;YACvD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA2B,IAAI;YAAoB;YAC/D,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA0B,IAAI;YAAsB;YAChE,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAmB,IAAI;YAAgB;YACnD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA0B,IAAI;YAAiB;YAC3D,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,UAAU,cAAc,SAAS,cAAc;IAErD,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,OAAO,EAAE;QAC3B,cAAc,OAAO,EAAE;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gBAAM,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,iCAAiC;;;;;;0BAGxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAEjD,cAAA,6LAAC;4BAAK,WAAW,aAAa,OAAO,gBAAgB;sCAClD,aAAa,OAAO,SAAS;;;;;;;;;;;oBAKjC,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,SAAS;wBACf,MAAM,UAAU;wBAChB,MAAM,UAAU;wBAChB,MAAM,WAAW,AAAC,OAAO,KAAK,GAAG,KAAK,EAAE,GAAI;wBAC5C,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBACtC,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBAEtC,qBACE,6LAAC;4BAAoB,WAAU;;8CAE7B,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,AAAC,GAAc,OAAZ,SAAS,IAAG;wCACtB,iBAAiB,OAAO,KAAK;wCAC7B,WAAW,AAAC,gCAA4C,OAAb,OAAO,KAAK,EAAC;wCACxD,iBAAiB;oCACnB;;;;;;8CAIF,6LAAC;oCACC,WAAW,AAAC,4IAEX,OADC,mBAAmB,OAAO,EAAE,GAAG,cAAc;oCAE/C,OAAO;wCACL,MAAM;wCACN,KAAK;oCACP;oCACA,SAAS,IAAM,kBAAkB;8CAEjC,cAAA,6LAAC;wCACC,WAAW,AAAC,mJAEX,OADC,mBAAmB,OAAO,EAAE,GAAG,sBAAsB;wCAEvD,OAAO;4CAAE,iBAAiB,OAAO,KAAK;wCAAC;kDAEvC,cAAA,6LAAC;4CAAK,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;sDACpF,OAAO,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;2BA9BpB,OAAO,EAAE;;;;;oBAoCvB;kCAGA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;GAtLwB;KAAA", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/EALayers.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface EALayersProps {\n  language: 'en' | 'ar';\n  onLayerClick: (layer: string) => void;\n}\n\nexport default function EALayers({ language, onLayerClick }: EALayersProps) {\n  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);\n  const [animationPhase, setAnimationPhase] = useState(0);\n\n  const layers = [\n    {\n      id: 'business',\n      name: { en: 'Business Architecture', ar: 'هندسة الأعمال' },\n      description: { en: 'Business processes, capabilities, and organization', ar: 'العمليات التجارية والقدرات والتنظيم' },\n      color: '#026c4a',\n      icon: '🏢'\n    },\n    {\n      id: 'information',\n      name: { en: 'Information Architecture', ar: 'هندسة المعلومات' },\n      description: { en: 'Data models, information flows, and governance', ar: 'نماذج البيانات وتدفقات المعلومات والحوكمة' },\n      color: '#0c402e',\n      icon: '📊'\n    },\n    {\n      id: 'application',\n      name: { en: 'Application Architecture', ar: 'هندسة التطبيقات' },\n      description: { en: 'Software applications and their interactions', ar: 'تطبيقات البرمجيات وتفاعلاتها' },\n      color: '#026c4a',\n      icon: '💻'\n    },\n    {\n      id: 'technology',\n      name: { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },\n      description: { en: 'Infrastructure, platforms, and technical standards', ar: 'البنية التحتية والمنصات والمعايير التقنية' },\n      color: '#0c402e',\n      icon: '⚙️'\n    }\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleLayerClick = (layer: any) => {\n    setSelectedLayer(layer.id);\n    onLayerClick(layer.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'Enterprise Architecture Layers' : 'طبقات هندسة المؤسسة'}\n      </h3>\n\n      <div className=\"relative w-full max-w-4xl\">\n        {/* 3D Isometric Layers */}\n        <div className=\"relative h-96 perspective-1000\">\n          {layers.map((layer, index) => {\n            const isActive = animationPhase === index;\n            const isSelected = selectedLayer === layer.id;\n            const zIndex = layers.length - index;\n            const translateY = index * -20;\n            const translateZ = index * 40;\n\n            return (\n              <div\n                key={layer.id}\n                className={`absolute inset-x-0 cursor-pointer transition-all duration-700 transform-gpu ${\n                  isSelected ? 'scale-105' : 'hover:scale-102'\n                }`}\n                style={{\n                  top: `${60 + index * 60}px`,\n                  height: '80px',\n                  zIndex: zIndex,\n                  transform: `translateY(${translateY}px) translateZ(${translateZ}px) rotateX(15deg)`,\n                  transformStyle: 'preserve-3d'\n                }}\n                onClick={() => handleLayerClick(layer)}\n              >\n                {/* Layer Base */}\n                <div\n                  className={`relative w-full h-full rounded-lg shadow-lg border-2 transition-all duration-500 ${\n                    isActive ? 'shadow-2xl border-white' : 'border-gray-300'\n                  } ${isSelected ? 'ring-4 ring-emerald-500' : ''}`}\n                  style={{\n                    backgroundColor: layer.color,\n                    background: `linear-gradient(135deg, ${layer.color} 0%, ${layer.color}dd 100%)`\n                  }}\n                >\n                  {/* Layer Content */}\n                  <div className={`flex items-center h-full px-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className={`flex-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <h4 className={`text-xl font-bold text-white mb-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.name[language]}\n                      </h4>\n                      <p className={`text-white/80 text-sm ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.description[language]}\n                      </p>\n                    </div>\n\n                    {/* Layer Indicator */}\n                    <div className={`w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold text-white ${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                      {index + 1}\n                    </div>\n                  </div>\n\n                  {/* Active Layer Highlight */}\n                  {isActive && (\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg animate-pulse\"></div>\n                  )}\n                </div>\n\n                {/* Layer Side (3D Effect) */}\n                <div\n                  className=\"absolute top-0 left-0 w-full h-full rounded-lg opacity-60\"\n                  style={{\n                    backgroundColor: layer.color,\n                    transform: 'translateY(-4px) translateX(4px)',\n                    zIndex: -1,\n                    filter: 'brightness(0.8)'\n                  }}\n                ></div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Layer Connections */}\n        <div className=\"absolute left-1/2 top-20 bottom-20 w-0.5 bg-gray-300 transform -translate-x-1/2 opacity-30\"></div>\n\n        {/* Side Labels */}\n        <div className={`absolute top-0 ${language === 'ar' ? 'left-0' : 'right-0'} h-full flex flex-col justify-center space-y-12 ${language === 'ar' ? 'pr-8' : 'pl-8'}`}>\n          {layers.map((layer, index) => (\n            <div\n              key={index}\n              className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}\n            >\n              <div\n                className={`w-4 h-4 rounded-full transition-all duration-500 ${\n                  animationPhase === index ? 'scale-150 shadow-lg' : ''\n                }`}\n                style={{\n                  backgroundColor: animationPhase === index ? layer.color : '#d1d5db'\n                }}\n              ></div>\n              <span className={`text-sm font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? `Layer ${index + 1}` : `الطبقة ${index + 1}`}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,SAAS,KAAyC;QAAzC,EAAE,QAAQ,EAAE,YAAY,EAAiB,GAAzC;;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAyB,IAAI;YAAgB;YACzD,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAAsC;YACnH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAkD,IAAI;YAA4C;YACrH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAgD,IAAI;YAA+B;YACtG,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA2B,IAAI;YAAoB;YAC/D,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAA4C;YACzH,OAAO;YACP,MAAM;QACR;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,WAAW;+CAAY;oBAC3B;uDAAkB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;;gBACzC;8CAAG;YACH;sCAAO,IAAM,cAAc;;QAC7B;6BAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,MAAM,EAAE;QACzB,aAAa,MAAM,EAAE;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gBAAM,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,mCAAmC;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;4BAClB,MAAM,WAAW,mBAAmB;4BACpC,MAAM,aAAa,kBAAkB,MAAM,EAAE;4BAC7C,MAAM,SAAS,OAAO,MAAM,GAAG;4BAC/B,MAAM,aAAa,QAAQ,CAAC;4BAC5B,MAAM,aAAa,QAAQ;4BAE3B,qBACE,6LAAC;gCAEC,WAAW,AAAC,+EAEX,OADC,aAAa,cAAc;gCAE7B,OAAO;oCACL,KAAK,AAAC,GAAkB,OAAhB,KAAK,QAAQ,IAAG;oCACxB,QAAQ;oCACR,QAAQ;oCACR,WAAW,AAAC,cAAyC,OAA5B,YAAW,mBAA4B,OAAX,YAAW;oCAChE,gBAAgB;gCAClB;gCACA,SAAS,IAAM,iBAAiB;;kDAGhC,6LAAC;wCACC,WAAW,AAAC,oFAER,OADF,WAAW,4BAA4B,mBACxC,KAA+C,OAA5C,aAAa,4BAA4B;wCAC7C,OAAO;4CACL,iBAAiB,MAAM,KAAK;4CAC5B,YAAY,AAAC,2BAA6C,OAAnB,MAAM,KAAK,EAAC,SAAmB,OAAZ,MAAM,KAAK,EAAC;wCACxE;;0DAGA,6LAAC;gDAAI,WAAW,AAAC,iCAAoF,OAApD,aAAa,OAAO,qBAAqB;;kEACxF,6LAAC;wDAAI,WAAW,AAAC,UAAwD,OAA/C,aAAa,OAAO,eAAe;;0EAC3D,6LAAC;gEAAG,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;0EACrF,MAAM,IAAI,CAAC,SAAS;;;;;;0EAEvB,6LAAC;gEAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;0EACxE,MAAM,WAAW,CAAC,SAAS;;;;;;;;;;;;kEAKhC,6LAAC;wDAAI,WAAW,AAAC,0FAA6H,OAApC,aAAa,OAAO,SAAS;kEACpI,QAAQ;;;;;;;;;;;;4CAKZ,0BACC,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAKnB,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,MAAM,KAAK;4CAC5B,WAAW;4CACX,QAAQ,CAAC;4CACT,QAAQ;wCACV;;;;;;;+BAtDG,MAAM,EAAE;;;;;wBA0DnB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAW,AAAC,kBAA4G,OAA3F,aAAa,OAAO,WAAW,WAAU,oDAAsF,OAApC,aAAa,OAAO,SAAS;kCACvJ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;gCAEC,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;kDAEzE,6LAAC;wCACC,WAAW,AAAC,oDAEX,OADC,mBAAmB,QAAQ,wBAAwB;wCAErD,OAAO;4CACL,iBAAiB,mBAAmB,QAAQ,MAAM,KAAK,GAAG;wCAC5D;;;;;;kDAEF,6LAAC;wCAAK,WAAW,AAAC,uBAAsE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC/H,aAAa,OAAO,AAAC,SAAkB,OAAV,QAAQ,KAAM,AAAC,UAAmB,OAAV,QAAQ;;;;;;;+BAZ3D;;;;;;;;;;;;;;;;;;;;;;AAoBnB;GAzJwB;KAAA", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/SpecificationView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface SpecificationViewProps {\n  framework: string;\n  domain: string;\n  language: 'en' | 'ar';\n  onBack: () => void;\n}\n\nexport default function SpecificationView({ framework, domain, language, onBack }: SpecificationViewProps) {\n  const [activeSection, setActiveSection] = useState('overview');\n\n  const specifications = {\n    'data-governance': {\n      en: {\n        title: 'Data Governance Specifications',\n        overview: 'Comprehensive framework for establishing data governance policies, procedures, and organizational structures.',\n        sections: [\n          {\n            id: 'policies',\n            title: 'Governance Policies',\n            content: 'Define data ownership, stewardship roles, and decision-making authority across the organization.'\n          },\n          {\n            id: 'standards',\n            title: 'Data Standards',\n            content: 'Establish consistent data definitions, formats, and quality requirements.'\n          },\n          {\n            id: 'compliance',\n            title: 'Compliance Framework',\n            content: 'Ensure adherence to regulatory requirements and industry standards.'\n          }\n        ]\n      },\n      ar: {\n        title: 'مواصفات حوكمة البيانات',\n        overview: 'إطار شامل لوضع سياسات وإجراءات وهياكل تنظيمية لحوكمة البيانات.',\n        sections: [\n          {\n            id: 'policies',\n            title: 'سياسات الحوكمة',\n            content: 'تحديد ملكية البيانات وأدوار الإشراف وسلطة اتخاذ القرار عبر المؤسسة.'\n          },\n          {\n            id: 'standards',\n            title: 'معايير البيانات',\n            content: 'وضع تعريفات وتنسيقات ومتطلبات جودة متسقة للبيانات.'\n          },\n          {\n            id: 'compliance',\n            title: 'إطار الامتثال',\n            content: 'ضمان الالتزام بالمتطلبات التنظيمية ومعايير الصناعة.'\n          }\n        ]\n      }\n    },\n    'data-architecture': {\n      en: {\n        title: 'Data Architecture Specifications',\n        overview: 'Technical blueprint for data systems, integration patterns, and infrastructure design.',\n        sections: [\n          {\n            id: 'design',\n            title: 'Architecture Design',\n            content: 'Define data flow patterns, system interfaces, and integration architectures.'\n          },\n          {\n            id: 'infrastructure',\n            title: 'Infrastructure Requirements',\n            content: 'Specify hardware, software, and network requirements for data systems.'\n          },\n          {\n            id: 'security',\n            title: 'Security Architecture',\n            content: 'Implement data protection, access controls, and encryption standards.'\n          }\n        ]\n      },\n      ar: {\n        title: 'مواصفات هندسة البيانات',\n        overview: 'مخطط تقني لأنظمة البيانات وأنماط التكامل وتصميم البنية التحتية.',\n        sections: [\n          {\n            id: 'design',\n            title: 'تصميم الهندسة',\n            content: 'تحديد أنماط تدفق البيانات وواجهات النظام وهندسة التكامل.'\n          },\n          {\n            id: 'infrastructure',\n            title: 'متطلبات البنية التحتية',\n            content: 'تحديد متطلبات الأجهزة والبرمجيات والشبكة لأنظمة البيانات.'\n          },\n          {\n            id: 'security',\n            title: 'هندسة الأمان',\n            content: 'تنفيذ حماية البيانات وضوابط الوصول ومعايير التشفير.'\n          }\n        ]\n      }\n    },\n    'business': {\n      en: {\n        title: 'Business Architecture Specifications',\n        overview: 'Strategic framework defining business capabilities, processes, and organizational structure.',\n        sections: [\n          {\n            id: 'capabilities',\n            title: 'Business Capabilities',\n            content: 'Define core business functions and their relationships across the enterprise.'\n          },\n          {\n            id: 'processes',\n            title: 'Process Architecture',\n            content: 'Map business processes, workflows, and operational procedures.'\n          },\n          {\n            id: 'organization',\n            title: 'Organizational Design',\n            content: 'Structure roles, responsibilities, and reporting relationships.'\n          }\n        ]\n      },\n      ar: {\n        title: 'مواصفات هندسة الأعمال',\n        overview: 'إطار استراتيجي يحدد قدرات الأعمال والعمليات والهيكل التنظيمي.',\n        sections: [\n          {\n            id: 'capabilities',\n            title: 'قدرات الأعمال',\n            content: 'تحديد وظائف الأعمال الأساسية وعلاقاتها عبر المؤسسة.'\n          },\n          {\n            id: 'processes',\n            title: 'هندسة العمليات',\n            content: 'رسم خريطة العمليات التجارية وتدفقات العمل والإجراءات التشغيلية.'\n          },\n          {\n            id: 'organization',\n            title: 'التصميم التنظيمي',\n            content: 'هيكلة الأدوار والمسؤوليات وعلاقات التقارير.'\n          }\n        ]\n      }\n    },\n    'application': {\n      en: {\n        title: 'Application Architecture Specifications',\n        overview: 'Technical framework for application design, integration, and lifecycle management.',\n        sections: [\n          {\n            id: 'design',\n            title: 'Application Design',\n            content: 'Define application components, interfaces, and integration patterns.'\n          },\n          {\n            id: 'integration',\n            title: 'Integration Architecture',\n            content: 'Specify APIs, messaging, and data exchange mechanisms.'\n          },\n          {\n            id: 'lifecycle',\n            title: 'Lifecycle Management',\n            content: 'Manage application development, deployment, and maintenance processes.'\n          }\n        ]\n      },\n      ar: {\n        title: 'مواصفات هندسة التطبيقات',\n        overview: 'إطار تقني لتصميم التطبيقات والتكامل وإدارة دورة الحياة.',\n        sections: [\n          {\n            id: 'design',\n            title: 'تصميم التطبيق',\n            content: 'تحديد مكونات التطبيق والواجهات وأنماط التكامل.'\n          },\n          {\n            id: 'integration',\n            title: 'هندسة التكامل',\n            content: 'تحديد واجهات برمجة التطبيقات والرسائل وآليات تبادل البيانات.'\n          },\n          {\n            id: 'lifecycle',\n            title: 'إدارة دورة الحياة',\n            content: 'إدارة عمليات تطوير التطبيقات ونشرها وصيانتها.'\n          }\n        ]\n      }\n    }\n  };\n\n  const currentSpec = specifications[domain as keyof typeof specifications]?.[language];\n\n  if (!currentSpec) {\n    return (\n      <div className=\"p-8 text-center\">\n        <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {language === 'en' ? 'Specification not available' : 'المواصفات غير متوفرة'}\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-8\">\n      {/* Header */}\n      <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n        <button\n          onClick={onBack}\n          className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n          style={{ color: 'var(--emerald-green)' }}\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n          </svg>\n        </button>\n        <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n          <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n            {currentSpec.title}\n          </h2>\n          <p className={`text-gray-600 mt-2 ${language === 'ar' ? 'font-arabic' : ''}`}>\n            {framework.toUpperCase()} Framework\n          </p>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className={`flex gap-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n        {/* Navigation */}\n        <div className=\"w-64\">\n          <div className=\"bg-white rounded-2xl shadow-lg p-6\">\n            <h3 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>\n              {language === 'en' ? 'Sections' : 'الأقسام'}\n            </h3>\n            <nav className=\"space-y-2\">\n              <button\n                onClick={() => setActiveSection('overview')}\n                className={`w-full text-left p-3 rounded-lg transition-colors ${\n                  activeSection === 'overview' ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'\n                } ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n              >\n                {language === 'en' ? 'Overview' : 'نظرة عامة'}\n              </button>\n              {currentSpec.sections.map((section) => (\n                <button\n                  key={section.id}\n                  onClick={() => setActiveSection(section.id)}\n                  className={`w-full text-left p-3 rounded-lg transition-colors ${\n                    activeSection === section.id ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'\n                  } ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                >\n                  {section.title}\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1\">\n          <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n            {activeSection === 'overview' ? (\n              <div>\n                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? 'Overview' : 'نظرة عامة'}\n                </h3>\n                <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {currentSpec.overview}\n                </p>\n              </div>\n            ) : (\n              <div>\n                {currentSpec.sections.map((section) => (\n                  activeSection === section.id && (\n                    <div key={section.id}>\n                      <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {section.title}\n                      </h3>\n                      <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {section.content}\n                      </p>\n                    </div>\n                  )\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,kBAAkB,KAA+D;QAA/D,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAA0B,GAA/D;QAsLpB;;IArLpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB;QACrB,mBAAmB;YACjB,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;YACA,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;QACA,qBAAqB;YACnB,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;YACA,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;QACA,YAAY;YACV,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;YACA,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;QACA,eAAe;YACb,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;YACA,IAAI;gBACF,OAAO;gBACP,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;IACF;IAEA,MAAM,eAAc,yBAAA,cAAc,CAAC,OAAsC,cAArD,6CAAA,sBAAuD,CAAC,SAAS;IAErF,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAE,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;0BAChE,aAAa,OAAO,gCAAgC;;;;;;;;;;;IAI7D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kCACjF,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAuB;kCAEvC,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kCAG/G,6LAAC;wBAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;0CAC9C,6LAAC;gCAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;gCAAM,OAAO;oCAAE,OAAO;gCAAuB;0CACnH,YAAY,KAAK;;;;;;0CAEpB,6LAAC;gCAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;;oCACrE,UAAU,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAW,AAAC,cAAiE,OAApD,aAAa,OAAO,qBAAqB;;kCAErE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAW,AAAC,0BAAgE,OAAvC,aAAa,OAAO,gBAAgB;oCAAM,OAAO;wCAAE,OAAO;oCAAuB;8CACvH,aAAa,OAAO,aAAa;;;;;;8CAEpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAW,AAAC,qDAER,OADF,kBAAkB,aAAa,mCAAmC,oBACnE,KAAqD,OAAlD,aAAa,OAAO,2BAA2B;sDAElD,aAAa,OAAO,aAAa;;;;;;wCAEnC,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACzB,6LAAC;gDAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;gDAC1C,WAAW,AAAC,qDAER,OADF,kBAAkB,QAAQ,EAAE,GAAG,mCAAmC,oBACnE,KAAqD,OAAlD,aAAa,OAAO,2BAA2B;0DAElD,QAAQ,KAAK;+CANT,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAczB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,2BACjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;wCAAM,OAAO;4CAAE,OAAO;wCAAuB;kDACxH,aAAa,OAAO,aAAa;;;;;;kDAEpC,6LAAC;wCAAE,WAAW,AAAC,iCAAuE,OAAvC,aAAa,OAAO,gBAAgB;kDAChF,YAAY,QAAQ;;;;;;;;;;;qDAIzB,6LAAC;0CACE,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,UACzB,kBAAkB,QAAQ,EAAE,kBAC1B,6LAAC;;0DACC,6LAAC;gDAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DACxH,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAE,WAAW,AAAC,iCAAuE,OAAvC,aAAa,OAAO,gBAAgB;0DAChF,QAAQ,OAAO;;;;;;;uCALV,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBxC;GA1RwB;KAAA", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\nimport DAMAWheel from '../../../components/DAMAWheel';\nimport EALayers from '../../../components/EALayers';\nimport SpecificationView from '../../../components/SpecificationView';\n\ninterface Framework {\n  id: string;\n  name: string;\n  country: 'saudi' | 'qatar';\n  type: 'data-management' | 'enterprise-architecture';\n}\n\nconst frameworks: Framework[] = [\n  { id: 'ndmo', name: 'NDMO', country: 'saudi', type: 'data-management' },\n  { id: 'npc', name: 'NPC', country: 'qatar', type: 'data-management' },\n  { id: 'noura', name: 'NOURA', country: 'saudi', type: 'enterprise-architecture' },\n  { id: 'gea', name: 'GEA', country: 'qatar', type: 'enterprise-architecture' }\n];\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedCategory, setSelectedCategory] = useState<'main' | 'data-management' | 'enterprise-architecture'>('main');\n  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);\n  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={frameworkIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        {selectedCategory === 'main' && (\n          <div className=\"px-12 py-20\">\n            {/* Two Larger Enhanced Cards */}\n            <div className={`flex gap-16 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n\n              {/* Data Management Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('data-management')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 right-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 left-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Data Management' : 'إدارة البيانات'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Standards' : 'الأطر والمعايير'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Enterprise Architecture Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('enterprise-architecture')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 left-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 right-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Models' : 'الأطر والنماذج'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Data Management Frameworks */}\n        {selectedCategory === 'data-management' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Data Management Frameworks' : 'أطر إدارة البيانات'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NDMO Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('ndmo')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NDMO\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Data Management Office' : 'مكتب إدارة البيانات الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* NPC Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('npc')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NPC\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Planning Council' : 'مجلس التخطيط الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enterprise Architecture Frameworks */}\n        {selectedCategory === 'enterprise-architecture' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Enterprise Architecture Frameworks' : 'أطر هندسة المؤسسة'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NOURA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('noura')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NOURA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Enterprise Architecture' : 'هندسة المؤسسة الوطنية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* GEA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('gea')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      GEA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Government Enterprise Architecture' : 'هندسة المؤسسة الحكومية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Framework Detail Views */}\n        {selectedFramework && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedFramework(null)}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {selectedFramework.toUpperCase()}\n              </h2>\n\n              {/* Country Tag */}\n              <div className={`px-3 py-1 rounded-full text-white text-sm font-bold flex items-center gap-2 ${language === 'ar' ? 'mr-4' : 'ml-4'}`}\n                style={{\n                  backgroundColor: frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '#16a34a' : '#dc2626'\n                }}\n              >\n                <span>{frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '🇸🇦' : '🇶🇦'}</span>\n                <span>\n                  {frameworks.find(f => f.id === selectedFramework)?.country === 'saudi'\n                    ? (language === 'en' ? 'Saudi Arabia' : 'السعودية')\n                    : (language === 'en' ? 'Qatar' : 'قطر')\n                  }\n                </span>\n              </div>\n            </div>\n\n            {/* DAMA Wheel for Data Management Frameworks */}\n            {(selectedFramework === 'ndmo' || selectedFramework === 'npc') && !selectedDomain && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <DAMAWheel\n                  language={language}\n                  onDomainClick={(domain) => {\n                    setSelectedDomain(domain);\n                  }}\n                />\n              </div>\n            )}\n\n            {/* EA Layers for Enterprise Architecture Frameworks */}\n            {(selectedFramework === 'noura' || selectedFramework === 'gea') && !selectedDomain && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <EALayers\n                  language={language}\n                  onLayerClick={(layer) => {\n                    setSelectedDomain(layer);\n                  }}\n                />\n              </div>\n            )}\n\n            {/* Specification View */}\n            {selectedDomain && selectedFramework && (\n              <div className=\"bg-white rounded-3xl shadow-lg\">\n                <SpecificationView\n                  framework={selectedFramework}\n                  domain={selectedDomain}\n                  language={language}\n                  onBack={() => setSelectedDomain(null)}\n                />\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAeA,MAAM,aAA0B;IAC9B;QAAE,IAAI;QAAQ,MAAM;QAAQ,SAAS;QAAS,MAAM;IAAkB;IACtE;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAAkB;IACpE;QAAE,IAAI;QAAS,MAAM;QAAS,SAAS;QAAS,MAAM;IAA0B;IAChF;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAA0B;CAC7E;AAEc,SAAS;QAgTW,kBAGZ,mBAEJ;;IApTjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;wCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,6LAAC;gBAAI,WAAU;;oBACZ,qBAAqB,wBACpB,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;8CAGtE,6LAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAwF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACrI,6LAAC;wDAAI,WAAU;wDAAiF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIhI,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACtF,aAAa,OAAO,oBAAoB;;;;;;kEAE3C,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,aAAa,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;8CAOxD,6LAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAyF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACtI,6LAAC;wDAAI,WAAU;wDAAkF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIjI,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACtF,aAAa,OAAO,4BAA4B;;;;;;kEAEnD,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,aAAa,OAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU1D,qBAAqB,mCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,+BAA+B;;;;;;;;;;;;0CAIxD,6LAAC;gCAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;kDAEtE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;kDAOjE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUhE,qBAAqB,2CACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,uCAAuC;;;;;;;;;;;;0CAIhE,6LAAC;gCAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;kDAEtE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;kDAOlE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUzE,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,kBAAkB,WAAW;;;;;;kDAIhC,6LAAC;wCAAI,WAAW,AAAC,+EAAkH,OAApC,aAAa,OAAO,SAAS;wCAC1H,OAAO;4CACL,iBAAiB,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,uCAAA,iBAAkD,OAAO,MAAK,UAAU,YAAY;wCACvG;;0DAEA,6LAAC;0DAAM,EAAA,oBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,wCAAA,kBAAkD,OAAO,MAAK,UAAU,SAAS;;;;;;0DACxF,6LAAC;0DACE,EAAA,oBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,wCAAA,kBAAkD,OAAO,MAAK,UAC1D,aAAa,OAAO,iBAAiB,aACrC,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;;4BAOxC,CAAC,sBAAsB,UAAU,sBAAsB,KAAK,KAAK,CAAC,gCACjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;oCACR,UAAU;oCACV,eAAe,CAAC;wCACd,kBAAkB;oCACpB;;;;;;;;;;;4BAML,CAAC,sBAAsB,WAAW,sBAAsB,KAAK,KAAK,CAAC,gCAClE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oCACP,UAAU;oCACV,cAAc,CAAC;wCACb,kBAAkB;oCACpB;;;;;;;;;;;4BAML,kBAAkB,mCACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0IAAA,CAAA,UAAiB;oCAChB,WAAW;oCACX,QAAQ;oCACR,UAAU;oCACV,QAAQ,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;GArWwB;KAAA", "debugId": null}}]}