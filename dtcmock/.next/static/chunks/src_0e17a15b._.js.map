{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/projects/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\n\ninterface Project {\n  id: string;\n  name: string;\n  description: string;\n  domain: 'Data' | 'EA';\n  country: 'Saudi' | 'Qatar';\n  consultant: string;\n  projectManager: string;\n  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';\n  startDate: string;\n  endDate: string;\n  progress: number;\n  createdAt: string;\n}\n\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n  role: 'admin' | 'consultant' | 'project-manager' | 'trainee';\n  status: 'active' | 'inactive';\n}\n\ninterface ProjectModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (project: Omit<Project, 'id' | 'createdAt' | 'progress'>) => void;\n  project?: Project | null;\n  language: 'en' | 'ar';\n  consultants: User[];\n  projectManagers: User[];\n}\n\nfunction ProjectModal({ isOpen, onClose, onSave, project, language, consultants, projectManagers }: ProjectModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    domain: 'Data' as 'Data' | 'EA',\n    country: 'Saudi' as 'Saudi' | 'Qatar',\n    consultant: '',\n    projectManager: '',\n    status: 'Planning' as Project['status'],\n    startDate: '',\n    endDate: ''\n  });\n\n  useEffect(() => {\n    if (project) {\n      setFormData({\n        name: project.name,\n        description: project.description,\n        domain: project.domain,\n        country: project.country,\n        consultant: project.consultant,\n        projectManager: project.projectManager,\n        status: project.status,\n        startDate: project.startDate,\n        endDate: project.endDate\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: '',\n        projectManager: '',\n        status: 'Planning',\n        startDate: '',\n        endDate: ''\n      });\n    }\n  }, [project, isOpen]);\n\n  const content = {\n    en: {\n      title: project ? 'Edit Project' : 'Create New Project',\n      projectName: 'Project Name',\n      projectDescription: 'Project Description',\n      domain: 'Domain',\n      country: 'Country',\n      consultant: 'Assign Consultant',\n      projectManager: 'Assign Project Manager',\n      status: 'Status',\n      startDate: 'Start Date',\n      endDate: 'End Date',\n      save: 'Save Project',\n      cancel: 'Cancel',\n      selectConsultant: 'Select Consultant',\n      selectProjectManager: 'Select Project Manager',\n      dataManagement: 'Data Management',\n      enterpriseArchitecture: 'Enterprise Architecture',\n      saudiArabia: 'Saudi Arabia',\n      qatar: 'Qatar',\n      planning: 'Planning',\n      inProgress: 'In Progress',\n      review: 'Review',\n      completed: 'Completed',\n      onHold: 'On Hold'\n    },\n    ar: {\n      title: project ? 'تعديل المشروع' : 'إنشاء مشروع جديد',\n      projectName: 'اسم المشروع',\n      projectDescription: 'وصف المشروع',\n      domain: 'المجال',\n      country: 'الدولة',\n      consultant: 'تعيين مستشار',\n      projectManager: 'تعيين مدير المشروع',\n      status: 'الحالة',\n      startDate: 'تاريخ البداية',\n      endDate: 'تاريخ النهاية',\n      save: 'حفظ المشروع',\n      cancel: 'إلغاء',\n      selectConsultant: 'اختر مستشار',\n      selectProjectManager: 'اختر مدير المشروع',\n      dataManagement: 'إدارة البيانات',\n      enterpriseArchitecture: 'هندسة المؤسسة',\n      saudiArabia: 'المملكة العربية السعودية',\n      qatar: 'قطر',\n      planning: 'التخطيط',\n      inProgress: 'قيد التنفيذ',\n      review: 'المراجعة',\n      completed: 'مكتمل',\n      onHold: 'معلق'\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(formData);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className={`bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n        <div className=\"p-8\">\n          <div className={`flex items-center justify-between mb-8 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n            <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].title}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 hover:bg-gray-100 rounded-xl transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Project Name */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].projectName}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n                required\n              />\n            </div>\n\n            {/* Project Description */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].projectDescription}\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                rows={4}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all resize-none ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n                required\n              />\n            </div>\n\n            {/* Domain and Country Row */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Domain */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].domain}\n                </label>\n                <select\n                  value={formData.domain}\n                  onChange={(e) => setFormData({ ...formData, domain: e.target.value as 'Data' | 'EA' })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                >\n                  <option value=\"Data\">{content[language].dataManagement}</option>\n                  <option value=\"EA\">{content[language].enterpriseArchitecture}</option>\n                </select>\n              </div>\n\n              {/* Country */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].country}\n                </label>\n                <select\n                  value={formData.country}\n                  onChange={(e) => setFormData({ ...formData, country: e.target.value as 'Saudi' | 'Qatar' })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                >\n                  <option value=\"Saudi\">{content[language].saudiArabia}</option>\n                  <option value=\"Qatar\">{content[language].qatar}</option>\n                </select>\n              </div>\n            </div>\n\n            {/* Consultant and Project Manager Row */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Consultant */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].consultant}\n                </label>\n                <select\n                  value={formData.consultant}\n                  onChange={(e) => setFormData({ ...formData, consultant: e.target.value })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                  required\n                >\n                  <option value=\"\">{content[language].selectConsultant}</option>\n                  {consultants.map((consultant) => (\n                    <option key={consultant.id} value={consultant.name}>\n                      {consultant.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Project Manager */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].projectManager}\n                </label>\n                <select\n                  value={formData.projectManager}\n                  onChange={(e) => setFormData({ ...formData, projectManager: e.target.value })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                  required\n                >\n                  <option value=\"\">{content[language].selectProjectManager}</option>\n                  {projectManagers.map((pm) => (\n                    <option key={pm.id} value={pm.name}>\n                      {pm.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Status and Dates Row */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {/* Status */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].status}\n                </label>\n                <select\n                  value={formData.status}\n                  onChange={(e) => setFormData({ ...formData, status: e.target.value as Project['status'] })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                >\n                  <option value=\"Planning\">{content[language].planning}</option>\n                  <option value=\"In Progress\">{content[language].inProgress}</option>\n                  <option value=\"Review\">{content[language].review}</option>\n                  <option value=\"Completed\">{content[language].completed}</option>\n                  <option value=\"On Hold\">{content[language].onHold}</option>\n                </select>\n              </div>\n\n              {/* Start Date */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].startDate}\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.startDate}\n                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                  required\n                />\n              </div>\n\n              {/* End Date */}\n              <div>\n                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].endDate}\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.endDate}\n                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                  style={{ focusRingColor: 'var(--emerald-green)' }}\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className={`flex gap-4 pt-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n              <button\n                type=\"submit\"\n                className={`flex-1 py-3 px-6 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'font-arabic' : ''}`}\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                {content[language].save}\n              </button>\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className={`flex-1 py-3 px-6 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'font-arabic' : ''}`}\n                style={{ borderColor: 'var(--charcoal-grey)', color: 'var(--charcoal-grey)' }}\n              >\n                {content[language].cancel}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Projects() {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [users, setUsers] = useState<User[]>([]);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterDomain, setFilterDomain] = useState<'All' | 'Data' | 'EA'>('All');\n  const [filterCountry, setFilterCountry] = useState<'All' | 'Saudi' | 'Qatar'>('All');\n  const [filterStatus, setFilterStatus] = useState<'All' | Project['status']>('All');\n\n  useEffect(() => {\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n\n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n\n    // Initialize mock users\n    const mockUsers: User[] = [\n      {\n        id: '1',\n        name: 'Ahmed Al-Rashid',\n        email: '<EMAIL>',\n        role: 'admin',\n        status: 'active'\n      },\n      {\n        id: '2',\n        name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active'\n      },\n      {\n        id: '3',\n        name: 'Mohammed Hassan',\n        email: '<EMAIL>',\n        role: 'project-manager',\n        status: 'active'\n      },\n      {\n        id: '4',\n        name: 'Emily Chen',\n        email: '<EMAIL>',\n        role: 'trainee',\n        status: 'inactive'\n      },\n      {\n        id: '5',\n        name: 'Omar Abdullah',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active'\n      },\n      {\n        id: '6',\n        name: 'Fatima Al-Zahra',\n        email: '<EMAIL>',\n        role: 'project-manager',\n        status: 'active'\n      },\n      {\n        id: '7',\n        name: 'David Wilson',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active'\n      },\n      {\n        id: '8',\n        name: 'Layla Mansouri',\n        email: '<EMAIL>',\n        role: 'project-manager',\n        status: 'active'\n      }\n    ];\n    setUsers(mockUsers);\n\n    // Initialize mock projects\n    const mockProjects: Project[] = [\n      {\n        id: '1',\n        name: 'NDMO Data Governance Implementation',\n        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'In Progress',\n        startDate: '2024-06-01',\n        endDate: '2024-12-31',\n        progress: 65,\n        createdAt: '2024-05-15'\n      },\n      {\n        id: '2',\n        name: 'Qatar GEA Enterprise Architecture',\n        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',\n        domain: 'EA',\n        country: 'Qatar',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'Planning',\n        startDate: '2024-09-01',\n        endDate: '2025-03-31',\n        progress: 15,\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '3',\n        name: 'NPC Coding Standards Rollout',\n        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',\n        domain: 'Data',\n        country: 'Qatar',\n        consultant: 'David Wilson',\n        projectManager: 'Layla Mansouri',\n        status: 'Review',\n        startDate: '2024-04-01',\n        endDate: '2024-10-31',\n        progress: 85,\n        createdAt: '2024-03-15'\n      },\n      {\n        id: '4',\n        name: 'NORA Infrastructure Modernization',\n        description: 'Modernizing national digital infrastructure following NORA framework specifications.',\n        domain: 'EA',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'Completed',\n        startDate: '2024-01-01',\n        endDate: '2024-07-31',\n        progress: 100,\n        createdAt: '2023-12-01'\n      },\n      {\n        id: '5',\n        name: 'Cross-Border Data Integration',\n        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'On Hold',\n        startDate: '2024-08-01',\n        endDate: '2025-02-28',\n        progress: 25,\n        createdAt: '2024-07-10'\n      }\n    ];\n    setProjects(mockProjects);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Projects',\n      subtitle: 'Project Management',\n      description: 'Manage and track digital transformation projects with comprehensive oversight and progress monitoring.',\n      createProject: 'Create New Project',\n      searchProjects: 'Search projects...',\n      allDomains: 'All Domains',\n      allCountries: 'All Countries',\n      allStatuses: 'All Statuses',\n      dataManagement: 'Data Management',\n      enterpriseArchitecture: 'Enterprise Architecture',\n      saudiArabia: 'Saudi Arabia',\n      qatar: 'Qatar',\n      planning: 'Planning',\n      inProgress: 'In Progress',\n      review: 'Review',\n      completed: 'Completed',\n      onHold: 'On Hold',\n      projectName: 'Project Name',\n      domain: 'Domain',\n      country: 'Country',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      status: 'Status',\n      progress: 'Progress',\n      actions: 'Actions',\n      edit: 'Edit',\n      delete: 'Delete',\n      noProjects: 'No projects found',\n      noProjectsDesc: 'No projects match your current filters. Try adjusting your search criteria.',\n      adminOnly: 'Admin Access Required',\n      adminOnlyDesc: 'Only administrators can create and manage projects.',\n      totalProjects: 'Total Projects',\n      activeProjects: 'Active Projects',\n      completedProjects: 'Completed Projects'\n    },\n    ar: {\n      title: 'المشاريع',\n      subtitle: 'إدارة المشاريع',\n      description: 'إدارة وتتبع مشاريع التحول الرقمي مع الإشراف الشامل ومراقبة التقدم.',\n      createProject: 'إنشاء مشروع جديد',\n      searchProjects: 'البحث في المشاريع...',\n      allDomains: 'جميع المجالات',\n      allCountries: 'جميع الدول',\n      allStatuses: 'جميع الحالات',\n      dataManagement: 'إدارة البيانات',\n      enterpriseArchitecture: 'هندسة المؤسسة',\n      saudiArabia: 'المملكة العربية السعودية',\n      qatar: 'قطر',\n      planning: 'التخطيط',\n      inProgress: 'قيد التنفيذ',\n      review: 'المراجعة',\n      completed: 'مكتمل',\n      onHold: 'معلق',\n      projectName: 'اسم المشروع',\n      domain: 'المجال',\n      country: 'الدولة',\n      consultant: 'المستشار',\n      projectManager: 'مدير المشروع',\n      status: 'الحالة',\n      progress: 'التقدم',\n      actions: 'الإجراءات',\n      edit: 'تعديل',\n      delete: 'حذف',\n      noProjects: 'لا توجد مشاريع',\n      noProjectsDesc: 'لا توجد مشاريع تطابق المرشحات الحالية. جرب تعديل معايير البحث.',\n      adminOnly: 'مطلوب صلاحية المدير',\n      adminOnlyDesc: 'يمكن للمديرين فقط إنشاء وإدارة المشاريع.',\n      totalProjects: 'إجمالي المشاريع',\n      activeProjects: 'المشاريع النشطة',\n      completedProjects: 'المشاريع المكتملة'\n    }\n  };\n\n  // Get consultants and project managers\n  const consultants = users.filter(user => user.role === 'consultant' && user.status === 'active');\n  const projectManagers = users.filter(user => user.role === 'project-manager' && user.status === 'active');\n\n  // Filter projects based on search and filters\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.consultant.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.projectManager.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesDomain = filterDomain === 'All' || project.domain === filterDomain;\n    const matchesCountry = filterCountry === 'All' || project.country === filterCountry;\n    const matchesStatus = filterStatus === 'All' || project.status === filterStatus;\n\n    return matchesSearch && matchesDomain && matchesCountry && matchesStatus;\n  });\n\n  // Project statistics\n  const totalProjects = projects.length;\n  const activeProjects = projects.filter(p => p.status === 'In Progress' || p.status === 'Planning').length;\n  const completedProjects = projects.filter(p => p.status === 'Completed').length;\n\n  // CRUD Functions\n  const handleAddProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'progress'>) => {\n    const newProject: Project = {\n      ...projectData,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString().split('T')[0],\n      progress: 0\n    };\n    setProjects([...projects, newProject]);\n  };\n\n  const handleEditProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'progress'>) => {\n    if (editingProject) {\n      setProjects(projects.map(project =>\n        project.id === editingProject.id\n          ? { ...project, ...projectData }\n          : project\n      ));\n      setEditingProject(null);\n    }\n  };\n\n  const handleDeleteProject = (projectId: string) => {\n    if (confirm(language === 'en' ? 'Are you sure you want to delete this project?' : 'هل أنت متأكد من حذف هذا المشروع؟')) {\n      setProjects(projects.filter(project => project.id !== projectId));\n    }\n  };\n\n  const openEditModal = (project: Project) => {\n    setEditingProject(project);\n    setIsModalOpen(true);\n  };\n\n  const getStatusColor = (status: Project['status']) => {\n    switch (status) {\n      case 'Planning': return 'bg-blue-100 text-blue-800';\n      case 'In Progress': return 'bg-yellow-100 text-yellow-800';\n      case 'Review': return 'bg-purple-100 text-purple-800';\n      case 'Completed': return 'bg-green-100 text-green-800';\n      case 'On Hold': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getDomainColor = (domain: 'Data' | 'EA') => {\n    return domain === 'Data'\n      ? 'bg-blue-50 text-blue-700 border-blue-200'\n      : 'bg-emerald-50 text-emerald-700 border-emerald-200';\n  };\n\n  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {\n    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';\n  };\n\n  const projectIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={projectIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        <div className=\"px-12 py-16\">\n          {/* Statistics Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-2xl font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{totalProjects}</p>\n                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].totalProjects}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 border border-yellow-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-2xl font-bold text-yellow-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{activeProjects}</p>\n                  <p className={`text-sm text-yellow-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].activeProjects}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-2xl font-bold text-green-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{completedProjects}</p>\n                  <p className={`text-sm text-green-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].completedProjects}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Header with Create Button */}\n          <div className={`flex items-center justify-between mb-8 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n            <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].title}\n            </h2>\n            {userRole === 'admin' && (\n              <button\n                onClick={() => {\n                  setEditingProject(null);\n                  setIsModalOpen(true);\n                }}\n                className={`flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                </svg>\n                {content[language].createProject}\n              </button>\n            )}\n          </div>\n\n          {/* Search and Filters */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\n            {/* Search */}\n            <div className=\"md:col-span-1\">\n              <input\n                type=\"text\"\n                placeholder={content[language].searchProjects}\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n              />\n            </div>\n\n            {/* Domain Filter */}\n            <div>\n              <select\n                value={filterDomain}\n                onChange={(e) => setFilterDomain(e.target.value as 'All' | 'Data' | 'EA')}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n              >\n                <option value=\"All\">{content[language].allDomains}</option>\n                <option value=\"Data\">{content[language].dataManagement}</option>\n                <option value=\"EA\">{content[language].enterpriseArchitecture}</option>\n              </select>\n            </div>\n\n            {/* Country Filter */}\n            <div>\n              <select\n                value={filterCountry}\n                onChange={(e) => setFilterCountry(e.target.value as 'All' | 'Saudi' | 'Qatar')}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n              >\n                <option value=\"All\">{content[language].allCountries}</option>\n                <option value=\"Saudi\">{content[language].saudiArabia}</option>\n                <option value=\"Qatar\">{content[language].qatar}</option>\n              </select>\n            </div>\n\n            {/* Status Filter */}\n            <div>\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as 'All' | Project['status'])}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}\n                style={{ focusRingColor: 'var(--emerald-green)' }}\n              >\n                <option value=\"All\">{content[language].allStatuses}</option>\n                <option value=\"Planning\">{content[language].planning}</option>\n                <option value=\"In Progress\">{content[language].inProgress}</option>\n                <option value=\"Review\">{content[language].review}</option>\n                <option value=\"Completed\">{content[language].completed}</option>\n                <option value=\"On Hold\">{content[language].onHold}</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Projects Table or Access Control */}\n          {userRole !== 'admin' ? (\n            // Non-admin users see access restriction\n            <div className=\"text-center py-16\">\n              <div\n                className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n                style={{ backgroundColor: 'var(--charcoal-grey)' }}\n              >\n                <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].adminOnly}\n              </h3>\n              <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {content[language].adminOnlyDesc}\n              </p>\n            </div>\n          ) : filteredProjects.length === 0 ? (\n            // No projects found\n            <div className=\"text-center py-16\">\n              <div\n                className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].noProjects}\n              </h3>\n              <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {content[language].noProjectsDesc}\n              </p>\n            </div>\n          ) : (\n            // Projects Table\n            <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200\">\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead style={{ backgroundColor: 'var(--emerald-green)' }}>\n                    <tr>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].projectName}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].domain}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].country}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].consultant}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].projectManager}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].status}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].progress}\n                      </th>\n                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>\n                        {content[language].actions}\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"divide-y divide-gray-200\">\n                    {filteredProjects.map((project, index) => (\n                      <tr key={project.id} className={`hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>\n                        <td className=\"px-6 py-4\">\n                          <div>\n                            <div className={`font-semibold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                              {project.name}\n                            </div>\n                            <div className={`text-sm text-gray-600 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                              {project.description.length > 60 ? `${project.description.substring(0, 60)}...` : project.description}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getDomainColor(project.domain)} ${language === 'ar' ? 'font-arabic' : ''}`}>\n                            {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                            <span className={`${language === 'ar' ? 'ml-2' : 'mr-2'}`}>{getCountryFlag(project.country)}</span>\n                            <span className={`text-sm font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                              {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}\n                            </span>\n                          </div>\n                        </td>\n                        <td className={`px-6 py-4 text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                          {project.consultant}\n                        </td>\n                        <td className={`px-6 py-4 text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                          {project.projectManager}\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>\n                            {content[language][project.status.toLowerCase().replace(' ', '') as keyof typeof content['en']]}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                              <div\n                                className=\"h-2 rounded-full transition-all duration-300\"\n                                style={{\n                                  width: `${project.progress}%`,\n                                  backgroundColor: 'var(--emerald-green)'\n                                }}\n                              ></div>\n                            </div>\n                            <span className={`ml-2 text-sm font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                              {project.progress}%\n                            </span>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className={`flex space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                            <button\n                              onClick={() => openEditModal(project)}\n                              className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\"\n                              title={content[language].edit}\n                            >\n                              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                              </svg>\n                            </button>\n                            <button\n                              onClick={() => handleDeleteProject(project.id)}\n                              className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n                              title={content[language].delete}\n                            >\n                              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                              </svg>\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Project Modal */}\n      <ProjectModal\n        isOpen={isModalOpen}\n        onClose={() => {\n          setIsModalOpen(false);\n          setEditingProject(null);\n        }}\n        onSave={editingProject ? handleEditProject : handleAddProject}\n        project={editingProject}\n        language={language}\n        consultants={consultants}\n        projectManagers={projectManagers}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAsCA,SAAS,aAAa,KAA+F;QAA/F,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAqB,GAA/F;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,aAAa,QAAQ,WAAW;oBAChC,QAAQ,QAAQ,MAAM;oBACtB,SAAS,QAAQ,OAAO;oBACxB,YAAY,QAAQ,UAAU;oBAC9B,gBAAgB,QAAQ,cAAc;oBACtC,QAAQ,QAAQ,MAAM;oBACtB,WAAW,QAAQ,SAAS;oBAC5B,SAAS,QAAQ,OAAO;gBAC1B;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;gBACX;YACF;QACF;iCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,UAAU;QACd,IAAI;YACF,OAAO,UAAU,iBAAiB;YAClC,aAAa;YACb,oBAAoB;YACpB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,kBAAkB;YAClB,sBAAsB;YACtB,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA,IAAI;YACF,OAAO,UAAU,kBAAkB;YACnC,aAAa;YACb,oBAAoB;YACpB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,kBAAkB;YAClB,sBAAsB;YACtB,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;QACP;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,AAAC,iFAA+H,OAA/C,aAAa,OAAO,eAAe;sBAClI,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;0CACjG,6LAAC;gCAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;gCAAM,OAAO;oCAAE,OAAO;gCAAuB;0CACnH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;0CAE1B,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;wCAAM,OAAO;4CAAE,OAAO;wCAAuB;kDAClI,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;kDAEhC,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACjE,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;wCACrK,OAAO;4CAAE,gBAAgB;wCAAuB;wCAChD,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;wCAAM,OAAO;4CAAE,OAAO;wCAAuB;kDAClI,OAAO,CAAC,SAAS,CAAC,kBAAkB;;;;;;kDAEvC,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACxE,MAAM;wCACN,WAAW,AAAC,uHAAwK,OAAlD,aAAa,OAAO,2BAA2B;wCACjL,OAAO;4CAAE,gBAAgB;wCAAuB;wCAChD,QAAQ;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;0DAE3B,6LAAC;gDACC,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAkB;gDACpF,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;;kEAEhD,6LAAC;wDAAO,OAAM;kEAAQ,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;kEACtD,6LAAC;wDAAO,OAAM;kEAAM,OAAO,CAAC,SAAS,CAAC,sBAAsB;;;;;;;;;;;;;;;;;;kDAKhE,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;0DAE5B,6LAAC;gDACC,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAsB;gDACzF,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;;kEAEhD,6LAAC;wDAAO,OAAM;kEAAS,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;kEACpD,6LAAC;wDAAO,OAAM;kEAAS,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMpD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;0DAE/B,6LAAC;gDACC,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACvE,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;gDAChD,QAAQ;;kEAER,6LAAC;wDAAO,OAAM;kEAAI,OAAO,CAAC,SAAS,CAAC,gBAAgB;;;;;;oDACnD,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;4DAA2B,OAAO,WAAW,IAAI;sEAC/C,WAAW,IAAI;2DADL,WAAW,EAAE;;;;;;;;;;;;;;;;;kDAQhC,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;0DAEnC,6LAAC;gDACC,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC3E,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;gDAChD,QAAQ;;kEAER,6LAAC;wDAAO,OAAM;kEAAI,OAAO,CAAC,SAAS,CAAC,oBAAoB;;;;;;oDACvD,gBAAgB,GAAG,CAAC,CAAC,mBACpB,6LAAC;4DAAmB,OAAO,GAAG,IAAI;sEAC/B,GAAG,IAAI;2DADG,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAS1B,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;0DAE3B,6LAAC;gDACC,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAsB;gDACxF,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;;kEAEhD,6LAAC;wDAAO,OAAM;kEAAY,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kEACpD,6LAAC;wDAAO,OAAM;kEAAe,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;kEACzD,6LAAC;wDAAO,OAAM;kEAAU,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAChD,6LAAC;wDAAO,OAAM;kEAAa,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;kEACtD,6LAAC;wDAAO,OAAM;kEAAW,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;kDAKrD,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;0DAE9B,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtE,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;gDAChD,QAAQ;;;;;;;;;;;;kDAKZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DAClI,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;0DAE5B,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACpE,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;gDACrK,OAAO;oDAAE,gBAAgB;gDAAuB;gDAChD,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,6LAAC;gCAAI,WAAW,AAAC,mBAA8D,OAA5C,aAAa,OAAO,qBAAqB;;kDAC1E,6LAAC;wCACC,MAAK;wCACL,WAAW,AAAC,mIAAyK,OAAvC,aAAa,OAAO,gBAAgB;wCAClL,OAAO;4CAAE,iBAAiB;wCAAuB;kDAEhD,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;kDAEzB,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAW,AAAC,iIAAuK,OAAvC,aAAa,OAAO,gBAAgB;wCAChL,OAAO;4CAAE,aAAa;4CAAwB,OAAO;wCAAuB;kDAE3E,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAlTS;KAAA;AAoTM,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAE5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;YACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YAExE,YAAY;YACZ,YAAY;YAEZ,wBAAwB;YACxB,MAAM,YAAoB;gBACxB;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;aACD;YACD,SAAS;YAET,2BAA2B;YAC3B,MAAM,eAA0B;gBAC9B;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;aACD;YACD,YAAY;QACd;6BAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,aAAa;YACb,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,SAAS;YACT,MAAM;YACN,QAAQ;YACR,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,mBAAmB;QACrB;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,aAAa;YACb,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,SAAS;YACT,MAAM;YACN,QAAQ;YACR,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,mBAAmB;QACrB;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,gBAAgB,KAAK,MAAM,KAAK;IACvF,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,qBAAqB,KAAK,MAAM,KAAK;IAEhG,8CAA8C;IAC9C,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,QAAQ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEzF,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QACnE,MAAM,iBAAiB,kBAAkB,SAAS,QAAQ,OAAO,KAAK;QACtE,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QAEnE,OAAO,iBAAiB,iBAAiB,kBAAkB;IAC7D;IAEA,qBAAqB;IACrB,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,iBAAiB,EAAE,MAAM,KAAK,YAAY,MAAM;IACzG,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IAE/E,iBAAiB;IACjB,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAsB;YAC1B,GAAG,WAAW;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD,UAAU;QACZ;QACA,YAAY;eAAI;YAAU;SAAW;IACvC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,gBAAgB;YAClB,YAAY,SAAS,GAAG,CAAC,CAAA,UACvB,QAAQ,EAAE,KAAK,eAAe,EAAE,GAC5B;oBAAE,GAAG,OAAO;oBAAE,GAAG,WAAW;gBAAC,IAC7B;YAEN,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,QAAQ,aAAa,OAAO,kDAAkD,qCAAqC;YACrH,YAAY,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,WAAW,SACd,6CACA;IACN;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,YAAY,UAAU,SAAS;IACxC;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,oCAA0E,OAAvC,aAAa,OAAO,gBAAgB;kEAAO;;;;;;kEAC7F,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAKvH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEAAO;;;;;;kEAC/F,6LAAC;wDAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAK1H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;kEAAO;;;;;;kEAC9F,6LAAC;wDAAE,WAAW,AAAC,0BAAgE,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9H,6LAAC;4BAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;8CACjG,6LAAC;oCAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;oCAAM,OAAO;wCAAE,OAAO;oCAAuB;8CACnH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;gCAEzB,aAAa,yBACZ,6LAAC;oCACC,SAAS;wCACP,kBAAkB;wCAClB,eAAe;oCACjB;oCACA,WAAW,AAAC,8IAAqM,OAAxD,aAAa,OAAO,iCAAiC;oCAC9M,OAAO;wCAAE,iBAAiB;oCAAuB;;sDAEjD,6LAAC;4CAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC1G,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,aAAa,OAAO,CAAC,SAAS,CAAC,cAAc;wCAC7C,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;wCACrK,OAAO;4CAAE,gBAAgB;wCAAuB;;;;;;;;;;;8CAKpD,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;wCACrK,OAAO;4CAAE,gBAAgB;wCAAuB;;0DAEhD,6LAAC;gDAAO,OAAM;0DAAO,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;0DACjD,6LAAC;gDAAO,OAAM;0DAAQ,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;0DACtD,6LAAC;gDAAO,OAAM;0DAAM,OAAO,CAAC,SAAS,CAAC,sBAAsB;;;;;;;;;;;;;;;;;8CAKhE,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;wCACrK,OAAO;4CAAE,gBAAgB;wCAAuB;;0DAEhD,6LAAC;gDAAO,OAAM;0DAAO,OAAO,CAAC,SAAS,CAAC,YAAY;;;;;;0DACnD,6LAAC;gDAAO,OAAM;0DAAS,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;0DACpD,6LAAC;gDAAO,OAAM;0DAAS,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;8CAKlD,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAW,AAAC,2GAA4J,OAAlD,aAAa,OAAO,2BAA2B;wCACrK,OAAO;4CAAE,gBAAgB;wCAAuB;;0DAEhD,6LAAC;gDAAO,OAAM;0DAAO,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;0DAClD,6LAAC;gDAAO,OAAM;0DAAY,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;0DACpD,6LAAC;gDAAO,OAAM;0DAAe,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;0DACzD,6LAAC;gDAAO,OAAM;0DAAU,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;0DAChD,6LAAC;gDAAO,OAAM;0DAAa,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;0DACtD,6LAAC;gDAAO,OAAM;0DAAW,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;wBAMtD,aAAa,UACZ,yCAAyC;sCACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEjD,cAAA,6LAAC;wCAAI,WAAU;wCAAY,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oCAAM,OAAO;wCAAE,OAAO;oCAAuB;8CACxH,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;8CAE9B,6LAAC;oCAAE,WAAW,AAAC,2CAAiF,OAAvC,aAAa,OAAO,gBAAgB;8CAC1F,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;;mCAGlC,iBAAiB,MAAM,KAAK,IAC9B,oBAAoB;sCACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEjD,cAAA,6LAAC;wCAAI,WAAU;wCAAY,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oCAAM,OAAO;wCAAE,OAAO;oCAAuB;8CACxH,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;8CAE/B,6LAAC;oCAAE,WAAW,AAAC,2CAAiF,OAAvC,aAAa,OAAO,gBAAgB;8CAC1F,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;mCAIrC,iBAAiB;sCACjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,OAAO;gDAAE,iBAAiB;4CAAuB;sDACtD,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;kEAEhC,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAE3B,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;kEAE5B,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;kEAE/B,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;kEAEnC,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAE3B,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kEAE7B,6LAAC;wDAAG,WAAW,AAAC,wDAAyG,OAAlD,aAAa,OAAO,2BAA2B;kEACnH,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;oDAAoB,WAAW,AAAC,sCAAoF,OAA/C,QAAQ,MAAM,IAAI,aAAa;;sEACnG,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;wEAAM,OAAO;4EAAE,OAAO;wEAAuB;kFAC/G,QAAQ,IAAI;;;;;;kFAEf,6LAAC;wEAAI,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;kFAC/E,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK,AAAC,GAAuC,OAArC,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,KAAI,SAAO,QAAQ,WAAW;;;;;;;;;;;;;;;;;sEAI3G,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,AAAC,8EAA+G,OAAlC,eAAe,QAAQ,MAAM,GAAE,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;0EAClK,QAAQ,MAAM,KAAK,SAAS,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,sBAAsB;;;;;;;;;;;sEAG5G,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;kFAC5E,6LAAC;wEAAK,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;kFAAW,eAAe,QAAQ,OAAO;;;;;;kFAC1F,6LAAC;wEAAK,WAAW,AAAC,uBAA6D,OAAvC,aAAa,OAAO,gBAAgB;wEAAM,OAAO;4EAAE,OAAO;wEAAuB;kFACtH,QAAQ,OAAO,KAAK,UAAU,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;sEAI5F,6LAAC;4DAAG,WAAW,AAAC,qBAA2D,OAAvC,aAAa,OAAO,gBAAgB;4DAAM,OAAO;gEAAE,OAAO;4DAAuB;sEAClH,QAAQ,UAAU;;;;;;sEAErB,6LAAC;4DAAG,WAAW,AAAC,qBAA2D,OAAvC,aAAa,OAAO,gBAAgB;4DAAM,OAAO;gEAAE,OAAO;4DAAuB;sEAClH,QAAQ,cAAc;;;;;;sEAEzB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,AAAC,uEAAwG,OAAlC,eAAe,QAAQ,MAAM,GAAE,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;0EAC3J,OAAO,CAAC,SAAS,CAAC,QAAQ,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,IAAkC;;;;;;;;;;;sEAGnG,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,AAAC,GAAmB,OAAjB,QAAQ,QAAQ,EAAC;gFAC3B,iBAAiB;4EACnB;;;;;;;;;;;kFAGJ,6LAAC;wEAAK,WAAW,AAAC,4BAAkE,OAAvC,aAAa,OAAO,gBAAgB;wEAAM,OAAO;4EAAE,OAAO;wEAAuB;;4EAC3H,QAAQ,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;sEAIxB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;;kFACzF,6LAAC;wEACC,SAAS,IAAM,cAAc;wEAC7B,WAAU;wEACV,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;kFAE7B,cAAA,6LAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;wEAC7C,WAAU;wEACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;kFAE/B,cAAA,6LAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDApEtE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoFnC,6LAAC;gBACC,QAAQ;gBACR,SAAS;oBACP,eAAe;oBACf,kBAAkB;gBACpB;gBACA,QAAQ,iBAAiB,oBAAoB;gBAC7C,SAAS;gBACT,UAAU;gBACV,aAAa;gBACb,iBAAiB;;;;;;;;;;;;AAIzB;IAlnBwB;MAAA", "debugId": null}}]}