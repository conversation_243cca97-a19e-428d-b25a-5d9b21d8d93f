{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport <PERSON> from '../../../components/Hero';\n\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n  role: 'admin' | 'consultant' | 'project-manager' | 'trainee';\n  status: 'active' | 'inactive';\n  createdAt: string;\n  lastLogin: string;\n}\n\ninterface UserModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (user: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => void;\n  user?: User | null;\n  language: 'en' | 'ar';\n}\n\nfunction UserModal({ isOpen, onClose, onSave, user, language }: UserModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    role: 'trainee' as User['role'],\n    status: 'active' as User['status']\n  });\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        role: user.role,\n        status: user.status\n      });\n    } else {\n      setFormData({\n        name: '',\n        email: '',\n        role: 'trainee',\n        status: 'active'\n      });\n    }\n  }, [user, isOpen]);\n\n  const content = {\n    en: {\n      addUser: 'Add New User',\n      editUser: 'Edit User',\n      name: 'Full Name',\n      email: 'Email Address',\n      role: 'Role',\n      status: 'Status',\n      active: 'Active',\n      inactive: 'Inactive',\n      admin: 'Admin',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      trainee: 'Trainee',\n      save: 'Save',\n      cancel: 'Cancel'\n    },\n    ar: {\n      addUser: 'إضافة مستخدم جديد',\n      editUser: 'تعديل المستخدم',\n      name: 'الاسم الكامل',\n      email: 'عنوان البريد الإلكتروني',\n      role: 'الدور',\n      status: 'الحالة',\n      active: 'نشط',\n      inactive: 'غير نشط',\n      admin: 'مدير',\n      consultant: 'مستشار',\n      projectManager: 'مدير مشروع',\n      trainee: 'متدرب',\n      save: 'حفظ',\n      cancel: 'إلغاء'\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(formData);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\" dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      <div className=\"bg-white rounded-2xl p-8 w-full max-w-md mx-4 shadow-2xl\">\n        <h2 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {user ? content[language].editUser : content[language].addUser}\n        </h2>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Name Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].name}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              required\n            />\n          </div>\n\n          {/* Email Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].email}\n            </label>\n            <input\n              type=\"email\"\n              value={formData.email}\n              onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              required\n            />\n          </div>\n\n          {/* Role Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].role}\n            </label>\n            <select\n              value={formData.role}\n              onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'] })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n            >\n              <option value=\"trainee\">{content[language].trainee}</option>\n              <option value=\"consultant\">{content[language].consultant}</option>\n              <option value=\"project-manager\">{content[language].projectManager}</option>\n              <option value=\"admin\">{content[language].admin}</option>\n            </select>\n          </div>\n\n          {/* Status Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].status}\n            </label>\n            <select\n              value={formData.status}\n              onChange={(e) => setFormData({ ...formData, status: e.target.value as User['status'] })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n            >\n              <option value=\"active\">{content[language].active}</option>\n              <option value=\"inactive\">{content[language].inactive}</option>\n            </select>\n          </div>\n\n          {/* Buttons */}\n          <div className={`flex gap-4 pt-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n            <button\n              type=\"submit\"\n              className={`flex-1 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              {content[language].save}\n            </button>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className={`flex-1 py-3 rounded-xl border border-gray-300 font-semibold transition-colors hover:bg-gray-50 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ color: 'var(--charcoal-grey)' }}\n            >\n              {content[language].cancel}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default function UserManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [users, setUsers] = useState<User[]>([]);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data initialization\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n\n    // Initialize with mock users\n    const mockUsers: User[] = [\n      {\n        id: '1',\n        name: 'Ahmed Al-Rashid',\n        email: '<EMAIL>',\n        role: 'admin',\n        status: 'active',\n        createdAt: '2024-01-15',\n        lastLogin: '2024-08-04'\n      },\n      {\n        id: '2',\n        name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active',\n        createdAt: '2024-02-20',\n        lastLogin: '2024-08-03'\n      },\n      {\n        id: '3',\n        name: 'Mohammed Hassan',\n        email: '<EMAIL>',\n        role: 'project-manager',\n        status: 'active',\n        createdAt: '2024-03-10',\n        lastLogin: '2024-08-02'\n      },\n      {\n        id: '4',\n        name: 'Emily Chen',\n        email: '<EMAIL>',\n        role: 'trainee',\n        status: 'inactive',\n        createdAt: '2024-04-05',\n        lastLogin: '2024-07-28'\n      },\n      {\n        id: '5',\n        name: 'Omar Abdullah',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active',\n        createdAt: '2024-05-12',\n        lastLogin: '2024-08-04'\n      }\n    ];\n    setUsers(mockUsers);\n  }, []);\n\n  // CRUD Functions\n  const handleAddUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {\n    const newUser: User = {\n      ...userData,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString().split('T')[0],\n      lastLogin: 'Never'\n    };\n    setUsers([...users, newUser]);\n  };\n\n  const handleEditUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {\n    if (editingUser) {\n      setUsers(users.map(user =>\n        user.id === editingUser.id\n          ? { ...user, ...userData }\n          : user\n      ));\n      setEditingUser(null);\n    }\n  };\n\n  const handleDeleteUser = (userId: string) => {\n    if (confirm(language === 'en' ? 'Are you sure you want to delete this user?' : 'هل أنت متأكد من حذف هذا المستخدم؟')) {\n      setUsers(users.filter(user => user.id !== userId));\n    }\n  };\n\n  const openEditModal = (user: User) => {\n    setEditingUser(user);\n    setIsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setEditingUser(null);\n  };\n\n  // Filter users based on search\n  const filteredUsers = users.filter(user =>\n    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const content = {\n    en: {\n      title: 'User Management',\n      subtitle: 'Administration',\n      description: 'Manage users, roles, and permissions across the platform with comprehensive control and oversight.',\n      addUser: 'Add User',\n      search: 'Search users...',\n      name: 'Name',\n      email: 'Email',\n      role: 'Role',\n      status: 'Status',\n      lastLogin: 'Last Login',\n      actions: 'Actions',\n      edit: 'Edit',\n      delete: 'Delete',\n      active: 'Active',\n      inactive: 'Inactive',\n      admin: 'Admin',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      trainee: 'Trainee',\n      totalUsers: 'Total Users',\n      activeUsers: 'Active Users',\n      inactiveUsers: 'Inactive Users'\n    },\n    ar: {\n      title: 'إدارة المستخدمين',\n      subtitle: 'الإدارة',\n      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة مع التحكم والإشراف الشامل.',\n      addUser: 'إضافة مستخدم',\n      search: 'البحث عن المستخدمين...',\n      name: 'الاسم',\n      email: 'البريد الإلكتروني',\n      role: 'الدور',\n      status: 'الحالة',\n      lastLogin: 'آخر تسجيل دخول',\n      actions: 'الإجراءات',\n      edit: 'تعديل',\n      delete: 'حذف',\n      active: 'نشط',\n      inactive: 'غير نشط',\n      admin: 'مدير',\n      consultant: 'مستشار',\n      projectManager: 'مدير مشروع',\n      trainee: 'متدرب',\n      totalUsers: 'إجمالي المستخدمين',\n      activeUsers: 'المستخدمون النشطون',\n      inactiveUsers: 'المستخدمون غير النشطين'\n    }\n  };\n\n  const userIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n    </svg>\n  );\n\n  const getRoleLabel = (role: User['role']) => {\n    switch (role) {\n      case 'admin': return content[language].admin;\n      case 'consultant': return content[language].consultant;\n      case 'project-manager': return content[language].projectManager;\n      case 'trainee': return content[language].trainee;\n      default: return role;\n    }\n  };\n\n  const getStatusBadge = (status: User['status']) => {\n    const isActive = status === 'active';\n    return (\n      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${\n        isActive\n          ? 'bg-green-100 text-green-800'\n          : 'bg-red-100 text-red-800'\n      } ${language === 'ar' ? 'font-arabic' : ''}`}>\n        {isActive ? content[language].active : content[language].inactive}\n      </span>\n    );\n  };\n\n  const activeUsers = users.filter(user => user.status === 'active').length;\n  const inactiveUsers = users.filter(user => user.status === 'inactive').length;\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Hero Section */}\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={userIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      {/* Main Content - User Management */}\n      <div className=\"bg-white\">\n        {/* Statistics Cards */}\n        <div className=\"px-12 py-8\">\n          <div className={`flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'} gap-6 mb-8`}>\n            <div className=\"flex-1 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl flex items-center justify-center text-white\" style={{ backgroundColor: 'var(--emerald-green)' }}>\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {users.length}\n                  </p>\n                  <p className={`text-sm font-semibold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].totalUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex-1 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-green-500 flex items-center justify-center text-white\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {activeUsers}\n                  </p>\n                  <p className={`text-sm font-semibold text-green-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].activeUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex-1 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 border border-red-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-red-500 flex items-center justify-center text-white\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {inactiveUsers}\n                  </p>\n                  <p className={`text-sm font-semibold text-red-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].inactiveUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Controls */}\n          <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n            <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <input\n                type=\"text\"\n                placeholder={content[language].search}\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className={`px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 w-80 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              />\n            </div>\n            <button\n              onClick={() => setIsModalOpen(true)}\n              className={`px-6 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse font-arabic' : 'flex-row'}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              {content[language].addUser}\n            </button>\n          </div>\n\n          {/* Users Table */}\n          <div className=\"bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-lg\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead style={{ backgroundColor: 'var(--emerald-green)' }}>\n                  <tr>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].name}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].email}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].role}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].status}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].lastLogin}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].actions}\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredUsers.map((user, index) => (\n                    <tr key={user.id} className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        <div className=\"font-semibold\" style={{ color: 'var(--charcoal-grey)' }}>\n                          {user.name}\n                        </div>\n                      </td>\n                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        {user.email}\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        <span className=\"px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800\">\n                          {getRoleLabel(user.role)}\n                        </span>\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                        {getStatusBadge(user.status)}\n                      </td>\n                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        {user.lastLogin}\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                        <div className={`flex gap-2 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                          <button\n                            onClick={() => openEditModal(user)}\n                            className=\"p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n                            title={content[language].edit}\n                          >\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors\"\n                            title={content[language].delete}\n                          >\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {filteredUsers.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n                  </svg>\n                </div>\n                <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en' ? 'No users found' : 'لم يتم العثور على مستخدمين'}\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* User Modal */}\n      <UserModal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        onSave={editingUser ? handleEditUser : handleAddUser}\n        user={editingUser}\n        language={language}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAuBA,SAAS,UAAU,KAA2D;QAA3D,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAkB,GAA3D;;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;IACV;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,UAAU;YACV,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA,IAAI;YACF,SAAS;YACT,UAAU;YACV,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;QACP;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;QAA6E,KAAK,aAAa,OAAO,QAAQ;kBAC3H,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAW,AAAC,2BAAqF,OAA3D,aAAa,OAAO,2BAA2B;oBAAe,OAAO;wBAAE,OAAO;oBAAuB;8BAC5I,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;8BAGhE,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,AAAC,oCAA8F,OAA3D,aAAa,OAAO,2BAA2B;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAW,AAAC,wGAAkK,OAA3D,aAAa,OAAO,2BAA2B;oCAClK,OAAO;wCAAE,mBAAmB;oCAAuB;oCACnD,QAAQ;;;;;;;;;;;;sCAKZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,AAAC,oCAA8F,OAA3D,aAAa,OAAO,2BAA2B;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;8CAE1B,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAW,AAAC,wGAAkK,OAA3D,aAAa,OAAO,2BAA2B;oCAClK,OAAO;wCAAE,mBAAmB;oCAAuB;oCACnD,QAAQ;;;;;;;;;;;;sCAKZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,AAAC,oCAA8F,OAA3D,aAAa,OAAO,2BAA2B;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,6LAAC;oCACC,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAiB;oCACjF,WAAW,AAAC,wGAAkK,OAA3D,aAAa,OAAO,2BAA2B;oCAClK,OAAO;wCAAE,mBAAmB;oCAAuB;;sDAEnD,6LAAC;4CAAO,OAAM;sDAAW,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;sDAClD,6LAAC;4CAAO,OAAM;sDAAc,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;sDACxD,6LAAC;4CAAO,OAAM;sDAAmB,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;sDACjE,6LAAC;4CAAO,OAAM;sDAAS,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKlD,6LAAC;;8CACC,6LAAC;oCAAM,WAAW,AAAC,oCAA8F,OAA3D,aAAa,OAAO,2BAA2B;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;8CAE3B,6LAAC;oCACC,OAAO,SAAS,MAAM;oCACtB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAmB;oCACrF,WAAW,AAAC,wGAAkK,OAA3D,aAAa,OAAO,2BAA2B;oCAClK,OAAO;wCAAE,mBAAmB;oCAAuB;;sDAEnD,6LAAC;4CAAO,OAAM;sDAAU,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;sDAChD,6LAAC;4CAAO,OAAM;sDAAY,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;;;;;;;sCAKxD,6LAAC;4BAAI,WAAW,AAAC,mBAAsE,OAApD,aAAa,OAAO,qBAAqB;;8CAC1E,6LAAC;oCACC,MAAK;oCACL,WAAW,AAAC,sFAA4H,OAAvC,aAAa,OAAO,gBAAgB;oCACrI,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEhD,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,AAAC,kGAAwI,OAAvC,aAAa,OAAO,gBAAgB;oCACjJ,OAAO;wCAAE,OAAO;oCAAuB;8CAEtC,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GAnKS;KAAA;AAqKM,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;YAEZ,6BAA6B;YAC7B,MAAM,YAAoB;gBACxB;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,WAAW;oBACX,WAAW;gBACb;aACD;YACD,SAAS;QACX;mCAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAgB;YACpB,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD,WAAW;QACb;QACA,SAAS;eAAI;YAAO;SAAQ;IAC9B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa;YACf,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC,IACvB;YAEN,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,aAAa,OAAO,+CAA+C,sCAAsC;YACnH,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC5C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG1D,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,eAAe;QACjB;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,eAAe;QACjB;IACF;IAEA,MAAM,yBACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;YAC5C,KAAK;gBAAc,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU;YACtD,KAAK;gBAAmB,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YAC/D,KAAK;gBAAW,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO;YAChD;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,WAAW;QAC5B,qBACE,6LAAC;YAAK,WAAW,AAAC,gDAId,OAHF,WACI,gCACA,2BACL,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;sBACrC,WAAW,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;IAGvE;IAEA,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACzE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IAE7E,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BAEpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAIH,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,AAAC,QAA2D,OAApD,aAAa,OAAO,qBAAqB,YAAW;;8CAC1E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,iBAAiB;gDAAuB;0DACjI,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAClH,MAAM,MAAM;;;;;;kEAEf,6LAAC;wDAAE,WAAW,AAAC,0CAAgF,OAAvC,aAAa,OAAO,gBAAgB;kEACzF,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAClH;;;;;;kEAEH,6LAAC;wDAAE,WAAW,AAAC,wCAA8E,OAAvC,aAAa,OAAO,gBAAgB;kEACvF,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;kEACzD,6LAAC;wDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAClH;;;;;;kEAEH,6LAAC;wDAAE,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACrF,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,6LAAC;4BAAI,WAAW,AAAC,0CAA6F,OAApD,aAAa,OAAO,qBAAqB;;8CACjG,6LAAC;oCAAI,WAAW,AAAC,2BAA8E,OAApD,aAAa,OAAO,qBAAqB;8CAClF,cAAA,6LAAC;wCACC,MAAK;wCACL,aAAa,OAAO,CAAC,SAAS,CAAC,MAAM;wCACrC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW,AAAC,oFAA8I,OAA3D,aAAa,OAAO,2BAA2B;wCAC9I,OAAO;4CAAE,mBAAmB;wCAAuB;;;;;;;;;;;8CAGvD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,AAAC,4GAA2K,OAAhE,aAAa,OAAO,iCAAiC;oCAC5K,OAAO;wCAAE,iBAAiB;oCAAuB;;sDAEjD,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;sCAK9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,OAAO;oDAAE,iBAAiB;gDAAuB;0DACtD,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sEAEzB,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;sEAE1B,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sEAEzB,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;sEAE3B,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;sEAE9B,6LAAC;4DAAG,WAAW,AAAC,sCAAgG,OAA3D,aAAa,OAAO,2BAA2B;sEACjG,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;0DAIhC,6LAAC;0DACE,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;wDAAiB,WAAW,AAAC,+DAA0G,OAA5C,QAAQ,MAAM,IAAI,aAAa;;0EACzH,6LAAC;gEAAG,WAAW,AAAC,aAAuE,OAA3D,aAAa,OAAO,2BAA2B;0EACzE,cAAA,6LAAC;oEAAI,WAAU;oEAAgB,OAAO;wEAAE,OAAO;oEAAuB;8EACnE,KAAK,IAAI;;;;;;;;;;;0EAGd,6LAAC;gEAAG,WAAW,AAAC,2BAAqF,OAA3D,aAAa,OAAO,2BAA2B;0EACtF,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAG,WAAW,AAAC,aAAuE,OAA3D,aAAa,OAAO,2BAA2B;0EACzE,cAAA,6LAAC;oEAAK,WAAU;8EACb,aAAa,KAAK,IAAI;;;;;;;;;;;0EAG3B,6LAAC;gEAAG,WAAW,AAAC,aAA2D,OAA/C,aAAa,OAAO,eAAe;0EAC5D,eAAe,KAAK,MAAM;;;;;;0EAE7B,6LAAC;gEAAG,WAAW,AAAC,2BAAqF,OAA3D,aAAa,OAAO,2BAA2B;0EACtF,KAAK,SAAS;;;;;;0EAEjB,6LAAC;gEAAG,WAAW,AAAC,aAA2D,OAA/C,aAAa,OAAO,eAAe;0EAC7D,cAAA,6LAAC;oEAAI,WAAW,AAAC,cAAiE,OAApD,aAAa,OAAO,qBAAqB;;sFACrE,6LAAC;4EACC,SAAS,IAAM,cAAc;4EAC7B,WAAU;4EACV,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;sFAE7B,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,6LAAC;4EACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4EACvC,WAAU;4EACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;sFAE/B,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDArCtE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;gCAgDvB,cAAc,MAAM,KAAK,mBACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAE,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;sDAChE,aAAa,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC;gBACC,QAAQ;gBACR,SAAS;gBACT,QAAQ,cAAc,iBAAiB;gBACvC,MAAM;gBACN,UAAU;;;;;;;;;;;;AAIlB;IAtYwB;MAAA", "debugId": null}}]}