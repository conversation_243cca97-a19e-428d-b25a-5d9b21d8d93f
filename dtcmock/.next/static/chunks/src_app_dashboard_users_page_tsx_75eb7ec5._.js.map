{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\n\nexport default function UserManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'User Management',\n      subtitle: 'Administration',\n      description: 'Manage users, roles, and permissions across the platform with comprehensive control and oversight.',\n      placeholder: 'User management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة المستخدمين',\n      subtitle: 'الإدارة',\n      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة مع التحكم والإشراف الشامل.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة المستخدمين هنا.'\n    }\n  };\n\n  const userIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <div className=\"mb-8\">\n        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].title}\n        </h1>\n        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-lg p-8 text-center card-shadow\">\n        <div\n          className=\"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n          </svg>\n        </div>\n        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].placeholder}\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;mCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,yBACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCACxH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;kCAE1B,6LAAC;wBAAE,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;kCAChE,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAIlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAEjD,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCACvG,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;AAKxC;GAvDwB;KAAA", "debugId": null}}]}