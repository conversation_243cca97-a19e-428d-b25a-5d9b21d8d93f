{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport Hero from '../../../../components/Hero';\n\ninterface Project {\n  id: string;\n  name: string;\n  description: string;\n  domain: 'Data' | 'EA';\n  country: 'Saudi' | 'Qatar';\n  consultant: string;\n  projectManager: string;\n  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';\n  startDate: string;\n  endDate: string;\n  progress: number;\n  createdAt: string;\n}\n\ninterface OrgNode {\n  id: string;\n  name: string;\n  title: string;\n  department: string;\n  level: number;\n  parentId?: string;\n  children?: OrgNode[];\n}\n\ninterface ClientInfo {\n  id: string;\n  type: 'Culture' | 'Internal Politics' | 'Business Process' | 'Technology' | 'Stakeholders' | 'Constraints';\n  title: string;\n  description: string;\n  priority: 'High' | 'Medium' | 'Low';\n  createdAt: string;\n}\n\ninterface ProjectTask {\n  id: string;\n  name: string;\n  description: string;\n  startDate: string;\n  endDate: string;\n  duration: number;\n  progress: number;\n  dependencies: string[];\n  assignee: string;\n  priority: 'High' | 'Medium' | 'Low';\n  status: 'Not Started' | 'In Progress' | 'Completed' | 'Blocked';\n}\n\nexport default function ProjectDetail() {\n  const params = useParams();\n  const projectId = params.id as string;\n  \n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [project, setProject] = useState<Project | null>(null);\n  const [activeTab, setActiveTab] = useState<'overview' | 'organization' | 'client-context' | 'project-plan'>('overview');\n  const [orgStructure, setOrgStructure] = useState<OrgNode[]>([]);\n  const [clientInfo, setClientInfo] = useState<ClientInfo[]>([]);\n  const [projectTasks, setProjectTasks] = useState<ProjectTask[]>([]);\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n\n    // Mock project data - in real app, fetch by ID\n    const mockProjects: Project[] = [\n      {\n        id: '1',\n        name: 'NDMO Data Governance Implementation',\n        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'In Progress',\n        startDate: '2024-06-01',\n        endDate: '2024-12-31',\n        progress: 65,\n        createdAt: '2024-05-15'\n      },\n      {\n        id: '2',\n        name: 'Qatar GEA Enterprise Architecture',\n        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',\n        domain: 'EA',\n        country: 'Qatar',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'Planning',\n        startDate: '2024-09-01',\n        endDate: '2025-03-31',\n        progress: 15,\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '3',\n        name: 'NPC Coding Standards Rollout',\n        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',\n        domain: 'Data',\n        country: 'Qatar',\n        consultant: 'David Wilson',\n        projectManager: 'Layla Mansouri',\n        status: 'Review',\n        startDate: '2024-04-01',\n        endDate: '2024-10-31',\n        progress: 85,\n        createdAt: '2024-03-15'\n      },\n      {\n        id: '4',\n        name: 'NORA Infrastructure Modernization',\n        description: 'Modernizing national digital infrastructure following NORA framework specifications.',\n        domain: 'EA',\n        country: 'Saudi',\n        consultant: 'Sarah Johnson',\n        projectManager: 'Mohammed Hassan',\n        status: 'Completed',\n        startDate: '2024-01-01',\n        endDate: '2024-07-31',\n        progress: 100,\n        createdAt: '2023-12-01'\n      },\n      {\n        id: '5',\n        name: 'Cross-Border Data Integration',\n        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',\n        domain: 'Data',\n        country: 'Saudi',\n        consultant: 'Omar Abdullah',\n        projectManager: 'Fatima Al-Zahra',\n        status: 'On Hold',\n        startDate: '2024-08-01',\n        endDate: '2025-02-28',\n        progress: 25,\n        createdAt: '2024-07-10'\n      }\n    ];\n\n    const foundProject = mockProjects.find(p => p.id === projectId);\n    setProject(foundProject || null);\n\n    // Mock organization structure\n    const mockOrgStructure: OrgNode[] = [\n      {\n        id: '1',\n        name: 'Ahmed Al-Rashid',\n        title: 'Chief Executive Officer',\n        department: 'Executive',\n        level: 1,\n        children: [\n          {\n            id: '2',\n            name: 'Sarah Johnson',\n            title: 'Chief Technology Officer',\n            department: 'Technology',\n            level: 2,\n            parentId: '1',\n            children: [\n              {\n                id: '3',\n                name: 'Mohammed Hassan',\n                title: 'Senior Project Manager',\n                department: 'Technology',\n                level: 3,\n                parentId: '2'\n              },\n              {\n                id: '4',\n                name: 'Emily Chen',\n                title: 'Lead Developer',\n                department: 'Technology',\n                level: 3,\n                parentId: '2'\n              }\n            ]\n          },\n          {\n            id: '5',\n            name: 'Omar Abdullah',\n            title: 'Chief Operations Officer',\n            department: 'Operations',\n            level: 2,\n            parentId: '1',\n            children: [\n              {\n                id: '6',\n                name: 'Fatima Al-Zahra',\n                title: 'Operations Manager',\n                department: 'Operations',\n                level: 3,\n                parentId: '5'\n              },\n              {\n                id: '7',\n                name: 'David Wilson',\n                title: 'Business Analyst',\n                department: 'Operations',\n                level: 3,\n                parentId: '5'\n              }\n            ]\n          }\n        ]\n      }\n    ];\n    setOrgStructure(mockOrgStructure);\n\n    // Mock client context information\n    const mockClientInfo: ClientInfo[] = [\n      {\n        id: '1',\n        type: 'Culture',\n        title: 'Hierarchical Decision Making',\n        description: 'The organization follows a traditional hierarchical structure where decisions are made at the top level and cascaded down. This affects project approval processes and change management.',\n        priority: 'High',\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '2',\n        type: 'Internal Politics',\n        title: 'Department Silos',\n        description: 'Strong departmental boundaries exist with limited cross-functional collaboration. IT and Business units often have conflicting priorities.',\n        priority: 'High',\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '3',\n        type: 'Technology',\n        title: 'Legacy System Dependencies',\n        description: 'Heavy reliance on legacy systems that are difficult to integrate with modern solutions. This creates technical constraints for the project.',\n        priority: 'Medium',\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '4',\n        type: 'Stakeholders',\n        title: 'Multiple Approval Layers',\n        description: 'Project decisions require approval from multiple stakeholders across different departments, which can slow down progress.',\n        priority: 'Medium',\n        createdAt: '2024-08-01'\n      },\n      {\n        id: '5',\n        type: 'Business Process',\n        title: 'Manual Approval Workflows',\n        description: 'Current business processes rely heavily on manual approvals and paper-based documentation, creating inefficiencies.',\n        priority: 'Low',\n        createdAt: '2024-08-01'\n      }\n    ];\n    setClientInfo(mockClientInfo);\n\n    // Mock project tasks\n    const mockProjectTasks: ProjectTask[] = [\n      {\n        id: '1',\n        name: 'Project Initiation',\n        description: 'Define project scope, objectives, and initial requirements gathering',\n        startDate: '2024-06-01',\n        endDate: '2024-06-15',\n        duration: 14,\n        progress: 100,\n        dependencies: [],\n        assignee: 'Mohammed Hassan',\n        priority: 'High',\n        status: 'Completed'\n      },\n      {\n        id: '2',\n        name: 'Stakeholder Analysis',\n        description: 'Identify and analyze all project stakeholders and their requirements',\n        startDate: '2024-06-16',\n        endDate: '2024-06-30',\n        duration: 14,\n        progress: 100,\n        dependencies: ['1'],\n        assignee: 'Sarah Johnson',\n        priority: 'High',\n        status: 'Completed'\n      },\n      {\n        id: '3',\n        name: 'Current State Assessment',\n        description: 'Assess current data governance practices and identify gaps',\n        startDate: '2024-07-01',\n        endDate: '2024-07-31',\n        duration: 30,\n        progress: 100,\n        dependencies: ['2'],\n        assignee: 'David Wilson',\n        priority: 'High',\n        status: 'Completed'\n      },\n      {\n        id: '4',\n        name: 'Framework Design',\n        description: 'Design the data governance framework based on NDMO standards',\n        startDate: '2024-08-01',\n        endDate: '2024-09-15',\n        duration: 45,\n        progress: 80,\n        dependencies: ['3'],\n        assignee: 'Omar Abdullah',\n        priority: 'High',\n        status: 'In Progress'\n      },\n      {\n        id: '5',\n        name: 'Policy Development',\n        description: 'Develop data governance policies and procedures',\n        startDate: '2024-09-16',\n        endDate: '2024-10-31',\n        duration: 45,\n        progress: 30,\n        dependencies: ['4'],\n        assignee: 'Fatima Al-Zahra',\n        priority: 'Medium',\n        status: 'In Progress'\n      },\n      {\n        id: '6',\n        name: 'Implementation Planning',\n        description: 'Create detailed implementation plan and timeline',\n        startDate: '2024-11-01',\n        endDate: '2024-11-30',\n        duration: 30,\n        progress: 0,\n        dependencies: ['5'],\n        assignee: 'Mohammed Hassan',\n        priority: 'Medium',\n        status: 'Not Started'\n      },\n      {\n        id: '7',\n        name: 'Training and Rollout',\n        description: 'Train staff and rollout the new data governance framework',\n        startDate: '2024-12-01',\n        endDate: '2024-12-31',\n        duration: 30,\n        progress: 0,\n        dependencies: ['6'],\n        assignee: 'Sarah Johnson',\n        priority: 'High',\n        status: 'Not Started'\n      }\n    ];\n    setProjectTasks(mockProjectTasks);\n  }, [projectId]);\n\n  const content = {\n    en: {\n      projectDetails: 'Project Details',\n      backToProjects: 'Back to Projects',\n      overview: 'Overview',\n      organization: 'Organization Structure',\n      clientContext: 'Client Context',\n      projectPlan: 'Project Plan',\n      projectNotFound: 'Project Not Found',\n      projectNotFoundDesc: 'The requested project could not be found.',\n      domain: 'Domain',\n      country: 'Country',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      status: 'Status',\n      progress: 'Progress',\n      startDate: 'Start Date',\n      endDate: 'End Date',\n      dataManagement: 'Data Management',\n      enterpriseArchitecture: 'Enterprise Architecture',\n      saudiArabia: 'Saudi Arabia',\n      qatar: 'Qatar'\n    },\n    ar: {\n      projectDetails: 'تفاصيل المشروع',\n      backToProjects: 'العودة للمشاريع',\n      overview: 'نظرة عامة',\n      organization: 'الهيكل التنظيمي',\n      clientContext: 'سياق العميل',\n      projectPlan: 'خطة المشروع',\n      projectNotFound: 'المشروع غير موجود',\n      projectNotFoundDesc: 'لم يتم العثور على المشروع المطلوب.',\n      domain: 'المجال',\n      country: 'الدولة',\n      consultant: 'المستشار',\n      projectManager: 'مدير المشروع',\n      status: 'الحالة',\n      progress: 'التقدم',\n      startDate: 'تاريخ البداية',\n      endDate: 'تاريخ النهاية',\n      dataManagement: 'إدارة البيانات',\n      enterpriseArchitecture: 'هندسة المؤسسة',\n      saudiArabia: 'المملكة العربية السعودية',\n      qatar: 'قطر'\n    }\n  };\n\n  if (!project) {\n    return (\n      <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n        <div className=\"bg-white min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div\n              className=\"w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl\"\n              style={{ backgroundColor: 'var(--charcoal-grey)' }}\n            >\n              <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].projectNotFound}\n            </h2>\n            <p className={`text-xl text-gray-600 max-w-2xl mx-auto mb-8 ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {content[language].projectNotFoundDesc}\n            </p>\n            <Link\n              href=\"/dashboard/projects\"\n              className={`inline-flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n              </svg>\n              {content[language].backToProjects}\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const getStatusColor = (status: Project['status']) => {\n    switch (status) {\n      case 'Planning': return 'bg-blue-100 text-blue-800';\n      case 'In Progress': return 'bg-yellow-100 text-yellow-800';\n      case 'Review': return 'bg-purple-100 text-purple-800';\n      case 'Completed': return 'bg-green-100 text-green-800';\n      case 'On Hold': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {\n    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';\n  };\n\n  const projectIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {\n    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';\n  };\n\n  const projectIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={project.name}\n        subtitle={content[language].projectDetails}\n        description={project.description}\n        icon={projectIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: language === 'en' ? 'Projects' : 'المشاريع', href: '/dashboard/projects' },\n          { label: project.name }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        <div className=\"px-12 py-8\">\n          {/* Back Button */}\n          <div className=\"mb-8\">\n            <Link\n              href=\"/dashboard/projects\"\n              className={`inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n            >\n              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n              </svg>\n              {content[language].backToProjects}\n            </Link>\n          </div>\n\n          {/* Project Summary Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {/* Domain Card */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].domain}</p>\n                  <p className={`text-lg font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Country Card */}\n            <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <span className=\"text-lg\">{getCountryFlag(project.country)}</span>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].country}</p>\n                  <p className={`text-lg font-bold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Status Card */}\n            <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].status}</p>\n                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {project.status}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Progress Card */}\n            <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                <div className=\"w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center text-white shadow-lg\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                  <p className={`text-sm text-orange-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].progress}</p>\n                  <p className={`text-lg font-bold text-orange-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{project.progress}%</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Tab Navigation */}\n          <div className=\"border-b border-gray-200 mb-8\">\n            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {[\n                { key: 'overview', label: content[language].overview, icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },\n                { key: 'organization', label: content[language].organization, icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },\n                { key: 'client-context', label: content[language].clientContext, icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2' },\n                { key: 'project-plan', label: content[language].projectPlan, icon: 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01' }\n              ].map((tab) => (\n                <button\n                  key={tab.key}\n                  onClick={() => setActiveTab(tab.key as any)}\n                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.key\n                      ? 'border-emerald-500 text-emerald-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                >\n                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={tab.icon} />\n                  </svg>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"min-h-[600px]\">\n            {activeTab === 'overview' && (\n              <div className=\"space-y-8\">\n                {/* Project Information */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    Project Information\n                  </h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].consultant}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.consultant}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].projectManager}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.projectManager}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].startDate}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.startDate}</p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        {content[language].endDate}\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.endDate}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Progress Visualization */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    Progress Overview\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className={`text-lg font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        Overall Progress\n                      </span>\n                      <span className={`text-lg font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>\n                        {project.progress}%\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-4\">\n                      <div\n                        className=\"h-4 rounded-full transition-all duration-300\"\n                        style={{\n                          width: `${project.progress}%`,\n                          background: 'linear-gradient(90deg, var(--emerald-green), var(--deep-emerald))'\n                        }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'organization' && (\n              <div className=\"space-y-8\">\n                {/* Organization Structure Header */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      Organization Structure\n                    </h3>\n                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                      <button\n                        className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                        style={{ backgroundColor: 'var(--emerald-green)' }}\n                      >\n                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                        </svg>\n                        Add Position\n                      </button>\n                      <button\n                        className={`flex items-center px-4 py-2 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                        style={{ borderColor: 'var(--emerald-green)', color: 'var(--emerald-green)' }}\n                      >\n                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                        </svg>\n                        Import from Excel\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Organization Chart */}\n                  <div className=\"overflow-x-auto\">\n                    <div className=\"min-w-[800px] p-8\">\n                      {/* CEO Level */}\n                      <div className=\"flex justify-center mb-8\">\n                        <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border-2 border-emerald-200 shadow-lg max-w-xs\">\n                          <div className=\"text-center\">\n                            <div className=\"w-16 h-16 bg-emerald-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg\">\n                              <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                              </svg>\n                            </div>\n                            <h4 className={`font-bold text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                              Ahmed Al-Rashid\n                            </h4>\n                            <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                              Chief Executive Officer\n                            </p>\n                            <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                              Executive\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Connection Line */}\n                      <div className=\"flex justify-center mb-8\">\n                        <div className=\"w-px h-8 bg-gray-300\"></div>\n                      </div>\n\n                      {/* Department Heads Level */}\n                      <div className=\"flex justify-center space-x-16 mb-8\">\n                        {/* CTO */}\n                        <div className=\"flex flex-col items-center\">\n                          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border-2 border-blue-200 shadow-lg max-w-xs\">\n                            <div className=\"text-center\">\n                              <div className=\"w-14 h-14 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg\">\n                                <svg className=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                </svg>\n                              </div>\n                              <h4 className={`font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                Sarah Johnson\n                              </h4>\n                              <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                Chief Technology Officer\n                              </p>\n                              <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                Technology\n                              </p>\n                            </div>\n                          </div>\n\n                          {/* CTO's Team */}\n                          <div className=\"mt-8 flex space-x-8\">\n                            <div className=\"bg-gradient-to-br from-blue-25 to-blue-50 rounded-xl p-4 border border-blue-100 shadow\">\n                              <div className=\"text-center\">\n                                <div className=\"w-10 h-10 bg-blue-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white\">\n                                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                  </svg>\n                                </div>\n                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                  Mohammed Hassan\n                                </h5>\n                                <p className={`text-xs text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  Senior Project Manager\n                                </p>\n                              </div>\n                            </div>\n                            <div className=\"bg-gradient-to-br from-blue-25 to-blue-50 rounded-xl p-4 border border-blue-100 shadow\">\n                              <div className=\"text-center\">\n                                <div className=\"w-10 h-10 bg-blue-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white\">\n                                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                  </svg>\n                                </div>\n                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                  Emily Chen\n                                </h5>\n                                <p className={`text-xs text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  Lead Developer\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* COO */}\n                        <div className=\"flex flex-col items-center\">\n                          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border-2 border-purple-200 shadow-lg max-w-xs\">\n                            <div className=\"text-center\">\n                              <div className=\"w-14 h-14 bg-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg\">\n                                <svg className=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                </svg>\n                              </div>\n                              <h4 className={`font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                Omar Abdullah\n                              </h4>\n                              <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                Chief Operations Officer\n                              </p>\n                              <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                Operations\n                              </p>\n                            </div>\n                          </div>\n\n                          {/* COO's Team */}\n                          <div className=\"mt-8 flex space-x-8\">\n                            <div className=\"bg-gradient-to-br from-purple-25 to-purple-50 rounded-xl p-4 border border-purple-100 shadow\">\n                              <div className=\"text-center\">\n                                <div className=\"w-10 h-10 bg-purple-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white\">\n                                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                  </svg>\n                                </div>\n                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                  Fatima Al-Zahra\n                                </h5>\n                                <p className={`text-xs text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  Operations Manager\n                                </p>\n                              </div>\n                            </div>\n                            <div className=\"bg-gradient-to-br from-purple-25 to-purple-50 rounded-xl p-4 border border-purple-100 shadow\">\n                              <div className=\"text-center\">\n                                <div className=\"w-10 h-10 bg-purple-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white\">\n                                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                                  </svg>\n                                </div>\n                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                  David Wilson\n                                </h5>\n                                <p className={`text-xs text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  Business Analyst\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'client-context' && (\n              <div className=\"space-y-8\">\n                {/* Client Working Domain */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    Client Working Domain\n                  </h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        Primary Domain\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {project.domain === 'Data' ? 'Data Management & Governance' : 'Enterprise Architecture & Digital Transformation'}\n                      </p>\n                    </div>\n                    <div>\n                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                        Geographic Focus\n                      </label>\n                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {project.country === 'Saudi' ? 'Saudi Arabia - Government Sector' : 'Qatar - Public & Private Sectors'}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"mt-6\">\n                    <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      Domain Description\n                    </label>\n                    <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {project.domain === 'Data'\n                        ? 'The client operates in the data management domain, focusing on establishing comprehensive data governance frameworks, implementing data quality standards, and ensuring compliance with national data regulations. The organization handles sensitive government data and requires robust security measures and privacy controls.'\n                        : 'The client specializes in enterprise architecture consulting, helping organizations design and implement strategic technology frameworks. They work closely with government entities to modernize digital infrastructure, establish architectural standards, and drive digital transformation initiatives across various sectors.'\n                      }\n                    </p>\n                  </div>\n                </div>\n\n                {/* Client Context Information */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      Client Context Information\n                    </h3>\n                    <button\n                      className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                      style={{ backgroundColor: 'var(--emerald-green)' }}\n                    >\n                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                      </svg>\n                      Add Context Info\n                    </button>\n                  </div>\n\n                  {/* Context Information Grid */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                    {clientInfo.map((info) => (\n                      <div key={info.id} className={`rounded-xl p-6 border-2 shadow-lg transition-all duration-200 hover:shadow-xl hover:-translate-y-1 ${\n                        info.type === 'Culture' ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200' :\n                        info.type === 'Internal Politics' ? 'bg-gradient-to-br from-red-50 to-red-100 border-red-200' :\n                        info.type === 'Technology' ? 'bg-gradient-to-br from-green-50 to-green-100 border-green-200' :\n                        info.type === 'Stakeholders' ? 'bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200' :\n                        info.type === 'Business Process' ? 'bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200' :\n                        'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'\n                      }`}>\n                        <div className={`flex items-center justify-between mb-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white shadow-lg ${\n                            info.type === 'Culture' ? 'bg-blue-500' :\n                            info.type === 'Internal Politics' ? 'bg-red-500' :\n                            info.type === 'Technology' ? 'bg-green-500' :\n                            info.type === 'Stakeholders' ? 'bg-purple-500' :\n                            info.type === 'Business Process' ? 'bg-orange-500' :\n                            'bg-gray-500'\n                          }`}>\n                            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              {info.type === 'Culture' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />}\n                              {info.type === 'Internal Politics' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />}\n                              {info.type === 'Technology' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />}\n                              {info.type === 'Stakeholders' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />}\n                              {info.type === 'Business Process' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />}\n                              {info.type === 'Constraints' && <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />}\n                            </svg>\n                          </div>\n                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                            info.priority === 'High' ? 'bg-red-100 text-red-800' :\n                            info.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-green-100 text-green-800'\n                          } ${language === 'ar' ? 'font-arabic' : ''}`}>\n                            {info.priority}\n                          </span>\n                        </div>\n                        <div>\n                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 ${\n                            info.type === 'Culture' ? 'bg-blue-100 text-blue-800' :\n                            info.type === 'Internal Politics' ? 'bg-red-100 text-red-800' :\n                            info.type === 'Technology' ? 'bg-green-100 text-green-800' :\n                            info.type === 'Stakeholders' ? 'bg-purple-100 text-purple-800' :\n                            info.type === 'Business Process' ? 'bg-orange-100 text-orange-800' :\n                            'bg-gray-100 text-gray-800'\n                          } ${language === 'ar' ? 'font-arabic' : ''}`}>\n                            {info.type}\n                          </span>\n                          <h4 className={`font-bold text-lg mb-3 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                            {info.title}\n                          </h4>\n                          <p className={`text-gray-700 text-sm leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n                            {info.description}\n                          </p>\n                        </div>\n                        <div className={`flex justify-end mt-4 space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                          <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\">\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </button>\n                          <button className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\">\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'project-plan' && (\n              <div className=\"space-y-8\">\n                {/* Project Plan Header */}\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8\">\n                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      Project Plan & Timeline\n                    </h3>\n                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                      <button\n                        className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                        style={{ backgroundColor: 'var(--emerald-green)' }}\n                      >\n                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                        </svg>\n                        Add Task\n                      </button>\n                      <button\n                        className={`flex items-center px-4 py-2 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}\n                        style={{ borderColor: 'var(--emerald-green)', color: 'var(--emerald-green)' }}\n                      >\n                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\n                        </svg>\n                        Export Plan\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Project Timeline Overview */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n                    <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-blue-700\">{projectTasks.length}</div>\n                        <div className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Total Tasks</div>\n                      </div>\n                    </div>\n                    <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-4 border border-yellow-200\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-yellow-700\">{projectTasks.filter(t => t.status === 'In Progress').length}</div>\n                        <div className={`text-sm text-yellow-600 ${language === 'ar' ? 'font-arabic' : ''}`}>In Progress</div>\n                      </div>\n                    </div>\n                    <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-green-700\">{projectTasks.filter(t => t.status === 'Completed').length}</div>\n                        <div className={`text-sm text-green-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Completed</div>\n                      </div>\n                    </div>\n                    <div className=\"bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-red-700\">{projectTasks.filter(t => t.status === 'Blocked').length}</div>\n                        <div className={`text-sm text-red-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Blocked</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Gantt Chart Visualization */}\n                  <div className=\"overflow-x-auto\">\n                    <div className=\"min-w-[1200px]\">\n                      {/* Timeline Header */}\n                      <div className=\"grid grid-cols-12 gap-2 mb-4\">\n                        <div className=\"col-span-4 text-sm font-medium text-gray-500 px-4 py-2\">Task Details</div>\n                        <div className=\"col-span-8 grid grid-cols-6 gap-1\">\n                          {['Jun 2024', 'Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024'].map((month) => (\n                            <div key={month} className=\"text-center text-sm font-medium text-gray-500 py-2 border-b border-gray-200\">\n                              {month}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Task Rows */}\n                      <div className=\"space-y-2\">\n                        {projectTasks.map((task, index) => (\n                          <div key={task.id} className=\"grid grid-cols-12 gap-2 items-center\">\n                            {/* Task Info */}\n                            <div className=\"col-span-4 bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n                              <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                                <div className=\"flex-1\">\n                                  <h4 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                                    {task.name}\n                                  </h4>\n                                  <p className={`text-xs text-gray-600 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                    {task.assignee}\n                                  </p>\n                                  <div className={`flex items-center mt-2 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                                      task.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                                      task.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :\n                                      task.status === 'Blocked' ? 'bg-red-100 text-red-800' :\n                                      'bg-gray-100 text-gray-800'\n                                    } ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                      {task.status}\n                                    </span>\n                                    <span className={`text-xs text-gray-500 ${language === 'ar' ? 'mr-2' : 'ml-2'} ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                      {task.duration}d\n                                    </span>\n                                  </div>\n                                </div>\n                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                                  task.priority === 'High' ? 'bg-red-100 text-red-800' :\n                                  task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :\n                                  'bg-green-100 text-green-800'\n                                } ${language === 'ar' ? 'font-arabic' : ''}`}>\n                                  {task.priority}\n                                </span>\n                              </div>\n                            </div>\n\n                            {/* Timeline Bar */}\n                            <div className=\"col-span-8 relative h-12\">\n                              <div className=\"absolute inset-0 grid grid-cols-6 gap-1\">\n                                {/* Timeline background */}\n                                {Array.from({ length: 6 }).map((_, monthIndex) => (\n                                  <div key={monthIndex} className=\"border-r border-gray-100 last:border-r-0\"></div>\n                                ))}\n                              </div>\n\n                              {/* Task Bar */}\n                              <div\n                                className={`absolute top-2 h-8 rounded-lg shadow-sm flex items-center justify-between px-3 ${\n                                  task.status === 'Completed' ? 'bg-gradient-to-r from-green-400 to-green-500' :\n                                  task.status === 'In Progress' ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :\n                                  task.status === 'Blocked' ? 'bg-gradient-to-r from-red-400 to-red-500' :\n                                  'bg-gradient-to-r from-gray-400 to-gray-500'\n                                }`}\n                                style={{\n                                  left: `${(index * 15) % 80}%`,\n                                  width: `${Math.min(task.duration * 2, 40)}%`\n                                }}\n                              >\n                                <span className=\"text-white text-xs font-medium\">{task.progress}%</span>\n                                {task.dependencies.length > 0 && (\n                                  <div className=\"flex items-center\">\n                                    <svg className=\"w-3 h-3 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n                                    </svg>\n                                  </div>\n                                )}\n                              </div>\n\n                              {/* Progress Bar */}\n                              <div\n                                className=\"absolute top-2 h-8 bg-white bg-opacity-30 rounded-lg\"\n                                style={{\n                                  left: `${(index * 15) % 80}%`,\n                                  width: `${Math.min(task.duration * 2, 40) * (task.progress / 100)}%`\n                                }}\n                              ></div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Dependencies Visualization */}\n                  <div className=\"mt-8\">\n                    <h4 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                      Task Dependencies\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                      {projectTasks.filter(task => task.dependencies.length > 0).map((task) => (\n                        <div key={task.id} className=\"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200\">\n                          <div className={`flex items-center mb-3 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                            <div className=\"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center text-white shadow-lg\">\n                              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n                              </svg>\n                            </div>\n                            <h5 className={`font-semibold text-sm ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                              {task.name}\n                            </h5>\n                          </div>\n                          <div className=\"space-y-2\">\n                            <p className={`text-xs text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Depends on:</p>\n                            {task.dependencies.map((depId) => {\n                              const depTask = projectTasks.find(t => t.id === depId);\n                              return depTask ? (\n                                <div key={depId} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>\n                                  <div className=\"w-2 h-2 bg-emerald-400 rounded-full\"></div>\n                                  <span className={`text-xs text-gray-700 ${language === 'ar' ? 'mr-2 font-arabic' : 'ml-2'}`}>\n                                    {depTask.name}\n                                  </span>\n                                </div>\n                              ) : null;\n                            })}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAuDe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAE3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmE;IAC5G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;YAEZ,+CAA+C;YAC/C,MAAM,eAA0B;gBAC9B;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;gBACb;aACD;YAED,MAAM,eAAe,aAAa,IAAI;wDAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACrD,WAAW,gBAAgB;YAE3B,8BAA8B;YAC9B,MAAM,mBAA8B;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,OAAO;oBACP,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,UAAU;gCACR;oCACE,IAAI;oCACJ,MAAM;oCACN,OAAO;oCACP,YAAY;oCACZ,OAAO;oCACP,UAAU;gCACZ;gCACA;oCACE,IAAI;oCACJ,MAAM;oCACN,OAAO;oCACP,YAAY;oCACZ,OAAO;oCACP,UAAU;gCACZ;6BACD;wBACH;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,UAAU;gCACR;oCACE,IAAI;oCACJ,MAAM;oCACN,OAAO;oCACP,YAAY;oCACZ,OAAO;oCACP,UAAU;gCACZ;gCACA;oCACE,IAAI;oCACJ,MAAM;oCACN,OAAO;oCACP,YAAY;oCACZ,OAAO;oCACP,UAAU;gCACZ;6BACD;wBACH;qBACD;gBACH;aACD;YACD,gBAAgB;YAEhB,kCAAkC;YAClC,MAAM,iBAA+B;gBACnC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;aACD;YACD,cAAc;YAEd,qBAAqB;YACrB,MAAM,mBAAkC;gBACtC;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc,EAAE;oBAChB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,cAAc;wBAAC;qBAAI;oBACnB,UAAU;oBACV,UAAU;oBACV,QAAQ;gBACV;aACD;YACD,gBAAgB;QAClB;kCAAG;QAAC;KAAU;IAEd,MAAM,UAAU;QACd,IAAI;YACF,gBAAgB;YAChB,gBAAgB;YAChB,UAAU;YACV,cAAc;YACd,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;QACT;QACA,IAAI;YACF,gBAAgB;YAChB,gBAAgB;YAChB,UAAU;YACV,cAAc;YACd,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,UAAU;YACV,WAAW;YACX,SAAS;YACT,gBAAgB;YAChB,wBAAwB;YACxB,aAAa;YACb,OAAO;QACT;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;sBACpD,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAAuB;sCAEjD,cAAA,6LAAC;gCAAI,WAAU;gCAAY,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACnE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;4BAAM,OAAO;gCAAE,OAAO;4BAAuB;sCACxH,OAAO,CAAC,SAAS,CAAC,eAAe;;;;;;sCAEpC,6LAAC;4BAAE,WAAW,AAAC,gDAAsF,OAAvC,aAAa,OAAO,gBAAgB;sCAC/F,OAAO,CAAC,SAAS,CAAC,mBAAmB;;;;;;sCAExC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,AAAC,qJAA4M,OAAxD,aAAa,OAAO,iCAAiC;4BACrN,OAAO;gCAAE,iBAAiB;4BAAuB;;8CAEjD,6LAAC;oCAAI,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACrH,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCAEtE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;IAM7C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,YAAY,UAAU,SAAS;IACxC;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,MAAM,iBAAiB,CAAC;QACtB,OAAO,YAAY,UAAU,SAAS;IACxC;IAEA,MAAM,4BACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,QAAQ,IAAI;gBACnB,UAAU,OAAO,CAAC,SAAS,CAAC,cAAc;gBAC1C,aAAa,QAAQ,WAAW;gBAChC,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,aAAa,OAAO,aAAa;wBAAY,MAAM;oBAAsB;oBAClF;wBAAE,OAAO,QAAQ,IAAI;oBAAC;iBACvB;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,AAAC,gFAAuI,OAAxD,aAAa,OAAO,iCAAiC;;kDAEhJ,6LAAC;wCAAI,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACrH,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCAEtE,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;sCAKrC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAC1G,6LAAC;wDAAE,WAAW,AAAC,mCAAyE,OAAvC,aAAa,OAAO,gBAAgB;kEAClF,QAAQ,MAAM,KAAK,SAAS,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;8CAOhH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAW,eAAe,QAAQ,OAAO;;;;;;;;;;;0DAE3D,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,4BAAkE,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;kEAC9G,6LAAC;wDAAE,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACrF,QAAQ,OAAO,KAAK,UAAU,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;8CAO9F,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;kEAC5G,6LAAC;wDAAK,WAAW,AAAC,uEAAwG,OAAlC,eAAe,QAAQ,MAAM,GAAE,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;kEAC3J,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAOvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;0DAC5E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAI,WAAW,AAAC,GAAsC,OAApC,aAAa,OAAO,SAAS;;kEAC9C,6LAAC;wDAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;kEAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kEAC9G,6LAAC;wDAAE,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;;4DAAO,QAAQ,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;0CACxF;oCACC;wCAAE,KAAK;wCAAY,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;wCAAE,MAAM;oCAAuM;oCACnQ;wCAAE,KAAK;wCAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,YAAY;wCAAE,MAAM;oCAAyQ;oCAC7U;wCAAE,KAAK;wCAAkB,OAAO,OAAO,CAAC,SAAS,CAAC,aAAa;wCAAE,MAAM;oCAAqL;oCAC5P;wCAAE,KAAK;wCAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,WAAW;wCAAE,MAAM;oCAAiK;iCACrO,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;wCACnC,WAAW,AAAC,gFAIR,OAHF,cAAc,IAAI,GAAG,GACjB,wCACA,8EACL,KAA2D,OAAxD,aAAa,OAAO,iCAAiC;;0DAEzD,6LAAC;gDAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC1G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAG,IAAI,IAAI;;;;;;;;;;;4CAE/E,IAAI,KAAK;;uCAXL,IAAI,GAAG;;;;;;;;;;;;;;;sCAkBpB,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,4BACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAAG;;;;;;8DAG9H,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;8EAE/B,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAAO,QAAQ,UAAU;;;;;;;;;;;;sEAExF,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;8EAEnC,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAAO,QAAQ,cAAc;;;;;;;;;;;;sEAE5F,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;8EAE9B,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAAO,QAAQ,SAAS;;;;;;;;;;;;sEAEvF,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAClI,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;8EAE5B,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAAO,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sDAMzF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAAG;;;;;;8DAG9H,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,AAAC,uBAA6D,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAAG;;;;;;8EAG5H,6LAAC;oEAAK,WAAW,AAAC,qBAA2D,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;;wEACpH,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;sEAGtB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,AAAC,GAAmB,OAAjB,QAAQ,QAAQ,EAAC;oEAC3B,YAAY;gEACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQX,cAAc,gCACb,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;kEACjG,6LAAC;wDAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAAG;;;;;;kEAGzH,6LAAC;wDAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;;0EACzF,6LAAC;gEACC,WAAW,AAAC,8IAAqM,OAAxD,aAAa,OAAO,iCAAiC;gEAC9M,OAAO;oEAAE,iBAAiB;gEAAuB;;kFAEjD,6LAAC;wEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;wEAAU,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1G,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;0EAGR,6LAAC;gEACC,WAAW,AAAC,4IAAmM,OAAxD,aAAa,OAAO,iCAAiC;gEAC5M,OAAO;oEAAE,aAAa;oEAAwB,OAAO;gEAAuB;;kFAE5E,6LAAC;wEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;wEAAU,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1G,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;;;;;;;;;;;;;0DAOZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,6LAAC;4EAAG,WAAW,AAAC,qBAA2D,OAAvC,aAAa,OAAO,gBAAgB;4EAAM,OAAO;gFAAE,OAAO;4EAAuB;sFAAG;;;;;;sFAGxH,6LAAC;4EAAE,WAAW,AAAC,4BAAkE,OAAvC,aAAa,OAAO,gBAAgB;sFAAM;;;;;;sFAGpF,6LAAC;4EAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sFAAM;;;;;;;;;;;;;;;;;;;;;;sEAQvF,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;;;;;;;;;;sEAIjB,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FAAI,WAAU;4FAAU,MAAK;4FAAO,QAAO;4FAAe,SAAQ;sGACjE,cAAA,6LAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAa;gGAAG,GAAE;;;;;;;;;;;;;;;;kGAGzE,6LAAC;wFAAG,WAAW,AAAC,aAAmD,OAAvC,aAAa,OAAO,gBAAgB;wFAAM,OAAO;4FAAE,OAAO;wFAAuB;kGAAG;;;;;;kGAGhH,6LAAC;wFAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kGAAM;;;;;;kGAGjF,6LAAC;wFAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kGAAM;;;;;;;;;;;;;;;;;sFAOrF,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGAAI,WAAU;oGAAU,MAAK;oGAAO,QAAO;oGAAe,SAAQ;8GACjE,cAAA,6LAAC;wGAAK,eAAc;wGAAQ,gBAAe;wGAAQ,aAAa;wGAAG,GAAE;;;;;;;;;;;;;;;;0GAGzE,6LAAC;gGAAG,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gGAAM,OAAO;oGAAE,OAAO;gGAAuB;0GAAG;;;;;;0GAG5H,6LAAC;gGAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;0GAAM;;;;;;;;;;;;;;;;;8FAKrF,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGAAI,WAAU;oGAAU,MAAK;oGAAO,QAAO;oGAAe,SAAQ;8GACjE,cAAA,6LAAC;wGAAK,eAAc;wGAAQ,gBAAe;wGAAQ,aAAa;wGAAG,GAAE;;;;;;;;;;;;;;;;0GAGzE,6LAAC;gGAAG,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gGAAM,OAAO;oGAAE,OAAO;gGAAuB;0GAAG;;;;;;0GAG5H,6LAAC;gGAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;0GAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EASzF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FAAI,WAAU;4FAAU,MAAK;4FAAO,QAAO;4FAAe,SAAQ;sGACjE,cAAA,6LAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAa;gGAAG,GAAE;;;;;;;;;;;;;;;;kGAGzE,6LAAC;wFAAG,WAAW,AAAC,aAAmD,OAAvC,aAAa,OAAO,gBAAgB;wFAAM,OAAO;4FAAE,OAAO;wFAAuB;kGAAG;;;;;;kGAGhH,6LAAC;wFAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;kGAAM;;;;;;kGAGnF,6LAAC;wFAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kGAAM;;;;;;;;;;;;;;;;;sFAOrF,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGAAI,WAAU;oGAAU,MAAK;oGAAO,QAAO;oGAAe,SAAQ;8GACjE,cAAA,6LAAC;wGAAK,eAAc;wGAAQ,gBAAe;wGAAQ,aAAa;wGAAG,GAAE;;;;;;;;;;;;;;;;0GAGzE,6LAAC;gGAAG,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gGAAM,OAAO;oGAAE,OAAO;gGAAuB;0GAAG;;;;;;0GAG5H,6LAAC;gGAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;0GAAM;;;;;;;;;;;;;;;;;8FAKvF,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGAAI,WAAU;oGAAU,MAAK;oGAAO,QAAO;oGAAe,SAAQ;8GACjE,cAAA,6LAAC;wGAAK,eAAc;wGAAQ,gBAAe;wGAAQ,aAAa;wGAAG,GAAE;;;;;;;;;;;;;;;;0GAGzE,6LAAC;gGAAG,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gGAAM,OAAO;oGAAE,OAAO;gGAAuB;0GAAG;;;;;;0GAG5H,6LAAC;gGAAE,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;0GAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCActG,cAAc,kCACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAAG;;;;;;8DAG9H,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAAG;;;;;;8EAGxI,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAC1D,QAAQ,MAAM,KAAK,SAAS,iCAAiC;;;;;;;;;;;;sEAGlE,6LAAC;;8EACC,6LAAC;oEAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;oEAAM,OAAO;wEAAE,OAAO;oEAAuB;8EAAG;;;;;;8EAGxI,6LAAC;oEAAE,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;8EAC1D,QAAQ,OAAO,KAAK,UAAU,qCAAqC;;;;;;;;;;;;;;;;;;8DAI1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAW,AAAC,kCAAwE,OAAvC,aAAa,OAAO,gBAAgB;4DAAM,OAAO;gEAAE,OAAO;4DAAuB;sEAAG;;;;;;sEAGxI,6LAAC;4DAAE,WAAW,AAAC,iCAAuE,OAAvC,aAAa,OAAO,gBAAgB;sEAChF,QAAQ,MAAM,KAAK,SAChB,sUACA;;;;;;;;;;;;;;;;;;sDAOV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;sEACjG,6LAAC;4DAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;4DAAM,OAAO;gEAAE,OAAO;4DAAuB;sEAAG;;;;;;sEAGzH,6LAAC;4DACC,WAAW,AAAC,8IAAqM,OAAxD,aAAa,OAAO,iCAAiC;4DAC9M,OAAO;gEAAE,iBAAiB;4DAAuB;;8EAEjD,6LAAC;oEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;oEAAU,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC1G,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;;8DAMV,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;4DAAkB,WAAW,AAAC,sGAO9B,OANC,KAAK,IAAI,KAAK,YAAY,+DAC1B,KAAK,IAAI,KAAK,sBAAsB,4DACpC,KAAK,IAAI,KAAK,eAAe,kEAC7B,KAAK,IAAI,KAAK,iBAAiB,qEAC/B,KAAK,IAAI,KAAK,qBAAqB,qEACnC;;8EAEA,6LAAC;oEAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;sFACjG,6LAAC;4EAAI,WAAW,AAAC,8EAOhB,OANC,KAAK,IAAI,KAAK,YAAY,gBAC1B,KAAK,IAAI,KAAK,sBAAsB,eACpC,KAAK,IAAI,KAAK,eAAe,iBAC7B,KAAK,IAAI,KAAK,iBAAiB,kBAC/B,KAAK,IAAI,KAAK,qBAAqB,kBACnC;sFAEA,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;;oFAChE,KAAK,IAAI,KAAK,2BAAa,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;oFAChG,KAAK,IAAI,KAAK,qCAAuB,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;oFAC1G,KAAK,IAAI,KAAK,8BAAgB,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;oFACnG,KAAK,IAAI,KAAK,gCAAkB,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;oFACrG,KAAK,IAAI,KAAK,oCAAsB,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;oFACzG,KAAK,IAAI,KAAK,+BAAiB,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;;;;;;;;;;;;sFAGzG,6LAAC;4EAAK,WAAW,AAAC,uEAId,OAHF,KAAK,QAAQ,KAAK,SAAS,4BAC3B,KAAK,QAAQ,KAAK,WAAW,kCAC7B,+BACD,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;sFACrC,KAAK,QAAQ;;;;;;;;;;;;8EAGlB,6LAAC;;sFACC,6LAAC;4EAAK,WAAW,AAAC,4EAOd,OANF,KAAK,IAAI,KAAK,YAAY,8BAC1B,KAAK,IAAI,KAAK,sBAAsB,4BACpC,KAAK,IAAI,KAAK,eAAe,gCAC7B,KAAK,IAAI,KAAK,iBAAiB,kCAC/B,KAAK,IAAI,KAAK,qBAAqB,kCACnC,6BACD,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;sFACrC,KAAK,IAAI;;;;;;sFAEZ,6LAAC;4EAAG,WAAW,AAAC,0BAAgE,OAAvC,aAAa,OAAO,gBAAgB;4EAAM,OAAO;gFAAE,OAAO;4EAAuB;sFACvH,KAAK,KAAK;;;;;;sFAEb,6LAAC;4EAAE,WAAW,AAAC,yCAA+E,OAAvC,aAAa,OAAO,gBAAgB;sFACxF,KAAK,WAAW;;;;;;;;;;;;8EAGrB,6LAAC;oEAAI,WAAW,AAAC,mCAA8F,OAA5D,aAAa,OAAO,qCAAqC;;sFAC1G,6LAAC;4EAAO,WAAU;sFAChB,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,6LAAC;4EAAO,WAAU;sFAChB,cAAA,6LAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;2DA5DnE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;gCAuE1B,cAAc,gCACb,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,0CAAqF,OAA5C,aAAa,OAAO,qBAAqB;;kEACjG,6LAAC;wDAAG,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAAG;;;;;;kEAGzH,6LAAC;wDAAI,WAAW,AAAC,kBAA6E,OAA5D,aAAa,OAAO,qCAAqC;;0EACzF,6LAAC;gEACC,WAAW,AAAC,8IAAqM,OAAxD,aAAa,OAAO,iCAAiC;gEAC9M,OAAO;oEAAE,iBAAiB;gEAAuB;;kFAEjD,6LAAC;wEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;wEAAU,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1G,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;0EAGR,6LAAC;gEACC,WAAW,AAAC,4IAAmM,OAAxD,aAAa,OAAO,iCAAiC;gEAC5M,OAAO;oEAAE,aAAa;oEAAwB,OAAO;gEAAuB;;kFAE5E,6LAAC;wEAAI,WAAW,AAAC,WAA8C,OAApC,aAAa,OAAO,SAAS;wEAAU,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1G,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;;;;;;;;;;;;;0DAOZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAoC,aAAa,MAAM;;;;;;8EACtE,6LAAC;oEAAI,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;8EAAM;;;;;;;;;;;;;;;;;kEAGvF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAsC,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;8EAChH,6LAAC;oEAAI,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;8EAAM;;;;;;;;;;;;;;;;;kEAGzF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;8EAC7G,6LAAC;oEAAI,WAAW,AAAC,0BAAgE,OAAvC,aAAa,OAAO,gBAAgB;8EAAM;;;;;;;;;;;;;;;;;kEAGxF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAmC,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;8EACzG,6LAAC;oEAAI,WAAW,AAAC,wBAA8D,OAAvC,aAAa,OAAO,gBAAgB;8EAAM;;;;;;;;;;;;;;;;;;;;;;;0DAMxF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAyD;;;;;;8EACxE,6LAAC;oEAAI,WAAU;8EACZ;wEAAC;wEAAY;wEAAY;wEAAY;wEAAY;wEAAY;qEAAW,CAAC,GAAG,CAAC,CAAC,sBAC7E,6LAAC;4EAAgB,WAAU;sFACxB;2EADO;;;;;;;;;;;;;;;;sEAQhB,6LAAC;4DAAI,WAAU;sEACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;oEAAkB,WAAU;;sFAE3B,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAW,AAAC,qCAAgF,OAA5C,aAAa,OAAO,qBAAqB;;kGAC5F,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAG,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gGAAM,OAAO;oGAAE,OAAO;gGAAuB;0GACtH,KAAK,IAAI;;;;;;0GAEZ,6LAAC;gGAAE,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;0GAC7E,KAAK,QAAQ;;;;;;0GAEhB,6LAAC;gGAAI,WAAW,AAAC,0BAAqE,OAA5C,aAAa,OAAO,qBAAqB;;kHACjF,6LAAC;wGAAK,WAAW,AAAC,uEAKd,OAJF,KAAK,MAAM,KAAK,cAAc,gCAC9B,KAAK,MAAM,KAAK,gBAAgB,kCAChC,KAAK,MAAM,KAAK,YAAY,4BAC5B,6BACD,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;kHACrC,KAAK,MAAM;;;;;;kHAEd,6LAAC;wGAAK,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,SAAS,QAAO,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;;4GAClH,KAAK,QAAQ;4GAAC;;;;;;;;;;;;;;;;;;;kGAIrB,6LAAC;wFAAK,WAAW,AAAC,uEAId,OAHF,KAAK,QAAQ,KAAK,SAAS,4BAC3B,KAAK,QAAQ,KAAK,WAAW,kCAC7B,+BACD,KAA0C,OAAvC,aAAa,OAAO,gBAAgB;kGACrC,KAAK,QAAQ;;;;;;;;;;;;;;;;;sFAMpB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAEZ,MAAM,IAAI,CAAC;wFAAE,QAAQ;oFAAE,GAAG,GAAG,CAAC,CAAC,GAAG,2BACjC,6LAAC;4FAAqB,WAAU;2FAAtB;;;;;;;;;;8FAKd,6LAAC;oFACC,WAAW,AAAC,kFAKX,OAJC,KAAK,MAAM,KAAK,cAAc,iDAC9B,KAAK,MAAM,KAAK,gBAAgB,mDAChC,KAAK,MAAM,KAAK,YAAY,6CAC5B;oFAEF,OAAO;wFACL,MAAM,AAAC,GAAoB,OAAlB,AAAC,QAAQ,KAAM,IAAG;wFAC3B,OAAO,AAAC,GAAkC,OAAhC,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,GAAG,KAAI;oFAC5C;;sGAEA,6LAAC;4FAAK,WAAU;;gGAAkC,KAAK,QAAQ;gGAAC;;;;;;;wFAC/D,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC1B,6LAAC;4FAAI,WAAU;sGACb,cAAA,6LAAC;gGAAI,WAAU;gGAAqB,MAAK;gGAAO,QAAO;gGAAe,SAAQ;0GAC5E,cAAA,6LAAC;oGAAK,eAAc;oGAAQ,gBAAe;oGAAQ,aAAa;oGAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8FAO7E,6LAAC;oFACC,WAAU;oFACV,OAAO;wFACL,MAAM,AAAC,GAAoB,OAAlB,AAAC,QAAQ,KAAM,IAAG;wFAC3B,OAAO,AAAC,GAA0D,OAAxD,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,GAAG,MAAM,CAAC,KAAK,QAAQ,GAAG,GAAG,GAAE;oFACpE;;;;;;;;;;;;;mEAzEI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0DAmFzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAW,AAAC,0BAAgE,OAAvC,aAAa,OAAO,gBAAgB;wDAAM,OAAO;4DAAE,OAAO;wDAAuB;kEAAG;;;;;;kEAG7H,6LAAC;wDAAI,WAAU;kEACZ,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC9D,6LAAC;gEAAkB,WAAU;;kFAC3B,6LAAC;wEAAI,WAAW,AAAC,0BAAqE,OAA5C,aAAa,OAAO,qBAAqB;;0FACjF,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFAAI,WAAU;oFAAU,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FACjE,cAAA,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;;;;;;;;;;;0FAGzE,6LAAC;gFAAG,WAAW,AAAC,yBAAwE,OAAhD,aAAa,OAAO,qBAAqB;gFAAU,OAAO;oFAAE,OAAO;gFAAuB;0FAC/H,KAAK,IAAI;;;;;;;;;;;;kFAGd,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;0FAAM;;;;;;4EAChF,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC;gFACtB,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gFAChD,OAAO,wBACL,6LAAC;oFAAgB,WAAW,AAAC,qBAAgE,OAA5C,aAAa,OAAO,qBAAqB;;sGACxF,6LAAC;4FAAI,WAAU;;;;;;sGACf,6LAAC;4FAAK,WAAW,AAAC,yBAAwE,OAAhD,aAAa,OAAO,qBAAqB;sGAChF,QAAQ,IAAI;;;;;;;mFAHP;;;;2FAMR;4EACN;;;;;;;;+DAvBM,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCzC;GAxlCwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}