{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedRole, setSelectedRole] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n\n  const roles = [\n    { value: 'admin', label: { en: 'Admin', ar: 'مدير' } },\n    { value: 'consultant', label: { en: 'Consultant', ar: 'مستشار' } },\n    { value: 'project-manager', label: { en: 'Project Manager', ar: 'مدير مشروع' } },\n    { value: 'trainee', label: { en: 'Trainee', ar: 'متدرب' } }\n  ];\n\n  const content = {\n    en: {\n      title: 'Welcome Back',\n      subtitle: 'Sign in to your account',\n      email: 'Email Address',\n      password: 'Password',\n      role: 'Select Role',\n      signin: 'Sign In',\n      forgotPassword: 'Forgot Password?',\n      companyName: 'DTC Accelerator',\n      tagline: 'Empowering Digital Transformation'\n    },\n    ar: {\n      title: 'مرحباً بعودتك',\n      subtitle: 'تسجيل الدخول إلى حسابك',\n      email: 'عنوان البريد الإلكتروني',\n      password: 'كلمة المرور',\n      role: 'اختر الدور',\n      signin: 'تسجيل الدخول',\n      forgotPassword: 'نسيت كلمة المرور؟',\n      companyName: 'مسرع التحول الرقمي',\n      tagline: 'تمكين التحول الرقمي'\n    }\n  };\n\n  const handleLogin = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Mock login validation - accept any email/password combination\n    if (email && password && selectedRole) {\n      // Store user data in localStorage (in real app, this would be handled by auth context)\n      localStorage.setItem('userRole', selectedRole);\n      localStorage.setItem('language', language);\n      localStorage.setItem('userEmail', email);\n\n      // Redirect to dashboard\n      router.push('/dashboard');\n    } else {\n      alert(language === 'en' ? 'Please fill in all fields' : 'يرجى ملء جميع الحقول');\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Left Section - 70% Animated Image/Graphics */}\n      <div className=\"w-[70%] relative overflow-hidden\" style={{ backgroundColor: 'var(--emerald-green)' }}>\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-white animate-slide-in-left\">\n            <div className=\"mb-8\">\n              <div className=\"w-32 h-32 mx-auto mb-6 rounded-full bg-white/10 flex items-center justify-center animate-pulse-subtle\">\n                <div className=\"w-16 h-16 rounded-full\" style={{ backgroundColor: 'var(--deep-emerald)' }}></div>\n              </div>\n              <h1 className={`text-4xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].companyName}</h1>\n              <p className={`text-xl opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].tagline}</p>\n            </div>\n\n            {/* Animated geometric shapes - RTL aware positioning */}\n            <div className={`absolute top-20 w-20 h-20 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-20' : 'left-20'}`}></div>\n            <div className={`absolute bottom-32 w-16 h-16 rounded-lg bg-white/10 animate-pulse-subtle ${language === 'ar' ? 'left-32' : 'right-32'}`} style={{ animationDelay: '1s' }}></div>\n            <div className={`absolute top-1/2 w-12 h-12 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-10' : 'left-10'}`} style={{ animationDelay: '2s' }}></div>\n          </div>\n        </div>\n\n        {/* Gradient overlay */}\n        <div className={`absolute inset-0 ${language === 'ar' ? 'bg-gradient-to-bl' : 'bg-gradient-to-br'} from-transparent to-black/20`}></div>\n      </div>\n\n      {/* Right Section - 30% Login Form */}\n      <div className=\"w-[30%] flex items-center justify-center p-8 bg-white\">\n        <div className=\"w-full max-w-md animate-fade-in-up\">\n          {/* Language Toggle */}\n          <div className={`flex mb-8 ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>\n            <button\n              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}\n              className=\"px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n              style={{\n                backgroundColor: 'var(--emerald-green)',\n                color: 'white'\n              }}\n            >\n              {language === 'en' ? 'العربية' : 'English'}\n            </button>\n          </div>\n\n          {/* Login Form */}\n          <div className={`text-center mb-8 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n            <h2 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].title}\n            </h2>\n            <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].subtitle}</p>\n          </div>\n\n          <form onSubmit={handleLogin} className=\"space-y-6\">\n            {/* Email Input */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].email}\n              </label>\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              />\n            </div>\n\n            {/* Password Input */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].password}\n              </label>\n              <input\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              />\n            </div>\n\n            {/* Role Selection */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].role}\n              </label>\n              <select\n                value={selectedRole}\n                onChange={(e) => setSelectedRole(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              >\n                <option value=\"\">{content[language].role}</option>\n                {roles.map((role) => (\n                  <option key={role.value} value={role.value}>\n                    {role.label[language]}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sign In Button */}\n            <button\n              type=\"submit\"\n              className={`w-full py-3 rounded-lg text-white font-medium transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              {content[language].signin}\n            </button>\n\n            {/* Forgot Password */}\n            <div className={`${language === 'ar' ? 'text-right' : 'text-center'}`}>\n              <button\n                type=\"button\"\n                className={`text-sm hover:underline ${language === 'ar' ? 'font-arabic' : ''}`}\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                {content[language].forgotPassword}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAS,OAAO;gBAAE,IAAI;gBAAS,IAAI;YAAO;QAAE;QACrD;YAAE,OAAO;YAAc,OAAO;gBAAE,IAAI;gBAAc,IAAI;YAAS;QAAE;QACjE;YAAE,OAAO;YAAmB,OAAO;gBAAE,IAAI;gBAAmB,IAAI;YAAa;QAAE;QAC/E;YAAE,OAAO;YAAW,OAAO;gBAAE,IAAI;gBAAW,IAAI;YAAQ;QAAE;KAC3D;IAED,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAEhB,gEAAgE;QAChE,IAAI,SAAS,YAAY,cAAc;YACrC,uFAAuF;YACvF,aAAa,OAAO,CAAC,YAAY;YACjC,aAAa,OAAO,CAAC,YAAY;YACjC,aAAa,OAAO,CAAC,aAAa;YAElC,wBAAwB;YACxB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,MAAM,aAAa,OAAO,8BAA8B;QAC1D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;QAAc,KAAK,aAAa,OAAO,QAAQ;;0BAE3H,6LAAC;gBAAI,WAAU;gBAAmC,OAAO;oBAAE,iBAAiB;gBAAuB;;kCACjG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAyB,OAAO;oDAAE,iBAAiB;gDAAsB;;;;;;;;;;;sDAE1F,6LAAC;4CAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;sDAAO,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;sDAClH,6LAAC;4CAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;sDAAO,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;8CAI1G,6LAAC;oCAAI,WAAW,AAAC,0EAAoH,OAA3C,aAAa,OAAO,aAAa;;;;;;8CAC3H,6LAAC;oCAAI,WAAW,AAAC,4EAAsH,OAA3C,aAAa,OAAO,YAAY;oCAAc,OAAO;wCAAE,gBAAgB;oCAAK;;;;;;8CACxK,6LAAC;oCAAI,WAAW,AAAC,2EAAqH,OAA3C,aAAa,OAAO,aAAa;oCAAa,OAAO;wCAAE,gBAAgB;oCAAK;;;;;;;;;;;;;;;;;kCAK3K,6LAAC;wBAAI,WAAW,AAAC,oBAAiF,OAA9D,aAAa,OAAO,sBAAsB,qBAAoB;;;;;;;;;;;;0BAIpG,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,AAAC,aAAgE,OAApD,aAAa,OAAO,kBAAkB;sCACjE,cAAA,6LAAC;gCACC,SAAS,IAAM,YAAY,aAAa,OAAO,OAAO;gCACtD,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;0CAEC,aAAa,OAAO,YAAY;;;;;;;;;;;sCAKrC,6LAAC;4BAAI,WAAW,AAAC,oBAAoE,OAAjD,aAAa,OAAO,eAAe;;8CACrE,6LAAC;oCAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;oCAAM,OAAO;wCAAE,OAAO;oCAAuB;8CACxH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;8CAE1B,6LAAC;oCAAE,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;8CAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;sCAGtG,6LAAC;4BAAK,UAAU;4BAAa,WAAU;;8CAErC,6LAAC;;sDACC,6LAAC;4CAAM,WAAW,AAAC,kCAA4F,OAA3D,aAAa,OAAO,2BAA2B;4CAAe,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;sDAE1B,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAW,AAAC,mHAA6K,OAA3D,aAAa,OAAO,2BAA2B;4CAC7K,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAW,AAAC,kCAA4F,OAA3D,aAAa,OAAO,2BAA2B;4CAAe,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;sDAE7B,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAW,AAAC,mHAA6K,OAA3D,aAAa,OAAO,2BAA2B;4CAC7K,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAW,AAAC,kCAA4F,OAA3D,aAAa,OAAO,2BAA2B;4CAAe,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sDAEzB,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAW,AAAC,mHAA6K,OAA3D,aAAa,OAAO,2BAA2B;4CAC7K,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAI,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;gDACvC,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wDAAwB,OAAO,KAAK,KAAK;kEACvC,KAAK,KAAK,CAAC,SAAS;uDADV,KAAK,KAAK;;;;;;;;;;;;;;;;;8CAQ7B,6LAAC;oCACC,MAAK;oCACL,WAAW,AAAC,oFAA0H,OAAvC,aAAa,OAAO,gBAAgB;oCACnI,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEhD,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;8CAI3B,6LAAC;oCAAI,WAAW,AAAC,GAAmD,OAAjD,aAAa,OAAO,eAAe;8CACpD,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;wCAC1E,OAAO;4CAAE,OAAO;wCAAuB;kDAEtC,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAlMwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}