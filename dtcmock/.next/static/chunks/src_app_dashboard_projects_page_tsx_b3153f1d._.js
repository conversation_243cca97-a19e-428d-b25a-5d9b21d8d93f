(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/dashboard/projects/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const e = new Error("Could not parse module '[project]/src/app/dashboard/projects/page.tsx'\n\nUnexpected eof");
e.code = 'MODULE_UNPARSABLE';
throw e;
}}),
}]);