{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\" style={{ backgroundColor: 'var(--emerald-green)' }}>\n      <div className=\"text-center text-white\">\n        <h1 className=\"text-5xl font-bold mb-6\">DTC Accelerator</h1>\n        <p className=\"text-xl mb-8 opacity-90\">Empowering Digital Transformation</p>\n        <Link\n          href=\"/login\"\n          className=\"inline-block px-8 py-4 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors\"\n        >\n          Go to Login\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;QAAgD,OAAO;YAAE,iBAAiB;QAAuB;kBAC9G,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;8BACvC,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}