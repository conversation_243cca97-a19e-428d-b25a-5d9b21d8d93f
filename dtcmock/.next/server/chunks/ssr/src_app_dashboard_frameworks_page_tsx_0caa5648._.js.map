{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <div className=\"mb-8\">\n        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].title}\n        </h1>\n        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-lg p-8 text-center card-shadow\">\n        <div\n          className=\"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n          </svg>\n        </div>\n        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].placeholder}\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACxH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;kCAE1B,8OAAC;wBAAE,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,gBAAgB,IAAI;kCACpE,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAEjD,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACvG,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}]}