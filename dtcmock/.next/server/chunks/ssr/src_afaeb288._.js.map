{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,eAAe,aAAa;QACvF,OAAO;YACL,YAAY;QACd;;0BAIA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,OAAO,YAAY,UAAU;;;;;;kCAC9G,8OAAC;wBAAI,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,YAAY,YAAY;;;;;;kCAC5H,8OAAC;wBAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,OAAO,aAAa,aAAa;;;;;;;;;;;;0BAItH,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAW,CAAC,kDAAkD,EAAE,aAAa,OAAO,qCAAqC,IAAI;sCAC9H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,8OAAC;4CACC,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;4CACtE,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;4BAEvF,sBACC,8OAAC;gCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,SAAS,QAAQ;0CACpE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAE,WAAW,CAAC,gEAAgE,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtH;;;;;;kDAKL,8OAAC;wCAAG,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACpH;;;;;;oCAIF,6BACC,8OAAC;wCAAE,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtG;;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/DAMAWheel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface DAMAWheelProps {\n  language: 'en' | 'ar';\n  onDomainClick: (domain: string) => void;\n}\n\nexport default function DAMAWheel({ language, onDomainClick }: DAMAWheelProps) {\n  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);\n\n  const domains = [\n    {\n      id: 'data-governance',\n      name: { en: 'Data Governance', ar: 'حوكمة البيانات' },\n      angle: 0,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-architecture',\n      name: { en: 'Data Architecture', ar: 'هندسة البيانات' },\n      angle: 45,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-modeling',\n      name: { en: 'Data Modeling', ar: 'نمذجة البيانات' },\n      angle: 90,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-storage',\n      name: { en: 'Data Storage', ar: 'تخزين البيانات' },\n      angle: 135,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-security',\n      name: { en: 'Data Security', ar: 'أمان البيانات' },\n      angle: 180,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-integration',\n      name: { en: 'Data Integration', ar: 'تكامل البيانات' },\n      angle: 225,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-quality',\n      name: { en: 'Data Quality', ar: 'جودة البيانات' },\n      angle: 270,\n      color: '#026c4a'\n    },\n    {\n      id: 'metadata',\n      name: { en: 'Metadata', ar: 'البيانات الوصفية' },\n      angle: 315,\n      color: '#0c402e'\n    }\n  ];\n\n  const handleDomainClick = (domain: any) => {\n    setSelectedDomain(domain.id);\n    onDomainClick(domain.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'DAMA Data Management Domains' : 'مجالات إدارة البيانات DAMA'}\n      </h3>\n\n      <div className=\"relative w-[500px] h-[500px] mx-auto\">\n        {/* Center Circle */}\n        <div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-2xl\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          <span className={language === 'ar' ? 'font-arabic' : ''}>\n            {language === 'en' ? 'DAMA' : 'داما'}\n          </span>\n        </div>\n\n        {/* Domain Segments */}\n        {domains.map((domain, index) => {\n          const radius = 180;\n          const centerX = 250;\n          const centerY = 250;\n          const angleRad = (domain.angle * Math.PI) / 180;\n          const x = centerX + radius * Math.cos(angleRad);\n          const y = centerY + radius * Math.sin(angleRad);\n\n          return (\n            <div key={domain.id}>\n              {/* Connection Line */}\n              <div\n                className=\"absolute top-1/2 left-1/2 origin-left h-0.5 opacity-30\"\n                style={{\n                  width: `${radius - 64}px`,\n                  backgroundColor: domain.color,\n                  transform: `translate(-50%, -50%) rotate(${domain.angle}deg)`,\n                  transformOrigin: 'left center'\n                }}\n              ></div>\n\n              {/* Domain Circle */}\n              <div\n                className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:scale-110 ${\n                  selectedDomain === domain.id ? 'scale-125 z-10' : ''\n                }`}\n                style={{\n                  left: x,\n                  top: y\n                }}\n                onClick={() => handleDomainClick(domain)}\n              >\n                <div\n                  className={`w-24 h-24 rounded-full flex items-center justify-center text-white font-semibold text-xs shadow-lg hover:shadow-2xl transition-all duration-300 ${\n                    selectedDomain === domain.id ? 'ring-4 ring-white' : ''\n                  }`}\n                  style={{ backgroundColor: domain.color }}\n                >\n                  <span className={`text-center leading-tight px-2 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {domain.name[language]}\n                  </span>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n\n        {/* Outer Ring */}\n        <div className=\"absolute inset-8 border-2 border-gray-200 rounded-full opacity-50\"></div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAkB;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAmB,IAAI;YAAiB;YACpD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAqB,IAAI;YAAiB;YACtD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAiB;YAClD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAiB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAgB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAoB,IAAI;YAAiB;YACrD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAgB;YAChD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAY,IAAI;YAAmB;YAC/C,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,OAAO,EAAE;QAC3B,cAAc,OAAO,EAAE;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;gBAAE,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,iCAAiC;;;;;;0BAGxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAEjD,cAAA,8OAAC;4BAAK,WAAW,aAAa,OAAO,gBAAgB;sCAClD,aAAa,OAAO,SAAS;;;;;;;;;;;oBAKjC,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,SAAS;wBACf,MAAM,UAAU;wBAChB,MAAM,UAAU;wBAChB,MAAM,WAAW,AAAC,OAAO,KAAK,GAAG,KAAK,EAAE,GAAI;wBAC5C,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBACtC,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBAEtC,qBACE,8OAAC;;8CAEC,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,GAAG,SAAS,GAAG,EAAE,CAAC;wCACzB,iBAAiB,OAAO,KAAK;wCAC7B,WAAW,CAAC,6BAA6B,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC;wCAC7D,iBAAiB;oCACnB;;;;;;8CAIF,8OAAC;oCACC,WAAW,CAAC,gHAAgH,EAC1H,mBAAmB,OAAO,EAAE,GAAG,mBAAmB,IAClD;oCACF,OAAO;wCACL,MAAM;wCACN,KAAK;oCACP;oCACA,SAAS,IAAM,kBAAkB;8CAEjC,cAAA,8OAAC;wCACC,WAAW,CAAC,gJAAgJ,EAC1J,mBAAmB,OAAO,EAAE,GAAG,sBAAsB,IACrD;wCACF,OAAO;4CAAE,iBAAiB,OAAO,KAAK;wCAAC;kDAEvC,cAAA,8OAAC;4CAAK,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDACxF,OAAO,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;2BA9BpB,OAAO,EAAE;;;;;oBAoCvB;kCAGA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/EALayers.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface EALayersProps {\n  language: 'en' | 'ar';\n  onLayerClick: (layer: string) => void;\n}\n\nexport default function EALayers({ language, onLayerClick }: EALayersProps) {\n  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);\n  const [animationPhase, setAnimationPhase] = useState(0);\n\n  const layers = [\n    {\n      id: 'business',\n      name: { en: 'Business Architecture', ar: 'هندسة الأعمال' },\n      description: { en: 'Business processes, capabilities, and organization', ar: 'العمليات التجارية والقدرات والتنظيم' },\n      color: '#026c4a',\n      icon: '🏢'\n    },\n    {\n      id: 'information',\n      name: { en: 'Information Architecture', ar: 'هندسة المعلومات' },\n      description: { en: 'Data models, information flows, and governance', ar: 'نماذج البيانات وتدفقات المعلومات والحوكمة' },\n      color: '#0c402e',\n      icon: '📊'\n    },\n    {\n      id: 'application',\n      name: { en: 'Application Architecture', ar: 'هندسة التطبيقات' },\n      description: { en: 'Software applications and their interactions', ar: 'تطبيقات البرمجيات وتفاعلاتها' },\n      color: '#026c4a',\n      icon: '💻'\n    },\n    {\n      id: 'technology',\n      name: { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },\n      description: { en: 'Infrastructure, platforms, and technical standards', ar: 'البنية التحتية والمنصات والمعايير التقنية' },\n      color: '#0c402e',\n      icon: '⚙️'\n    }\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleLayerClick = (layer: any) => {\n    setSelectedLayer(layer.id);\n    onLayerClick(layer.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'Enterprise Architecture Layers' : 'طبقات هندسة المؤسسة'}\n      </h3>\n\n      <div className=\"relative w-full max-w-4xl\">\n        {/* 3D Isometric Layers */}\n        <div className=\"relative h-96 perspective-1000\">\n          {layers.map((layer, index) => {\n            const isActive = animationPhase === index;\n            const isSelected = selectedLayer === layer.id;\n            const zIndex = layers.length - index;\n            const translateY = index * -20;\n            const translateZ = index * 40;\n\n            return (\n              <div\n                key={layer.id}\n                className={`absolute inset-x-0 cursor-pointer transition-all duration-700 transform-gpu ${\n                  isSelected ? 'scale-105' : 'hover:scale-102'\n                }`}\n                style={{\n                  top: `${60 + index * 60}px`,\n                  height: '80px',\n                  zIndex: zIndex,\n                  transform: `translateY(${translateY}px) translateZ(${translateZ}px) rotateX(15deg)`,\n                  transformStyle: 'preserve-3d'\n                }}\n                onClick={() => handleLayerClick(layer)}\n              >\n                {/* Layer Base */}\n                <div\n                  className={`relative w-full h-full rounded-lg shadow-lg border-2 transition-all duration-500 ${\n                    isActive ? 'shadow-2xl border-white' : 'border-gray-300'\n                  } ${isSelected ? 'ring-4 ring-emerald-500' : ''}`}\n                  style={{\n                    backgroundColor: layer.color,\n                    background: `linear-gradient(135deg, ${layer.color} 0%, ${layer.color}dd 100%)`\n                  }}\n                >\n                  {/* Layer Content */}\n                  <div className={`flex items-center h-full px-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className={`flex-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <h4 className={`text-xl font-bold text-white mb-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.name[language]}\n                      </h4>\n                      <p className={`text-white/80 text-sm ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.description[language]}\n                      </p>\n                    </div>\n\n                    {/* Layer Indicator */}\n                    <div className={`w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold text-white ${language === 'ar' ? 'mr-4' : 'ml-4'}`}>\n                      {index + 1}\n                    </div>\n                  </div>\n\n                  {/* Active Layer Highlight */}\n                  {isActive && (\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg animate-pulse\"></div>\n                  )}\n                </div>\n\n                {/* Layer Side (3D Effect) */}\n                <div\n                  className=\"absolute top-0 left-0 w-full h-full rounded-lg opacity-60\"\n                  style={{\n                    backgroundColor: layer.color,\n                    transform: 'translateY(-4px) translateX(4px)',\n                    zIndex: -1,\n                    filter: 'brightness(0.8)'\n                  }}\n                ></div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Layer Connections */}\n        <div className=\"absolute left-1/2 top-20 bottom-20 w-0.5 bg-gray-300 transform -translate-x-1/2 opacity-30\"></div>\n\n        {/* Side Labels */}\n        <div className={`absolute top-0 ${language === 'ar' ? 'left-0' : 'right-0'} h-full flex flex-col justify-center space-y-12 ${language === 'ar' ? 'pr-8' : 'pl-8'}`}>\n          {layers.map((layer, index) => (\n            <div\n              key={index}\n              className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}\n            >\n              <div\n                className={`w-4 h-4 rounded-full transition-all duration-500 ${\n                  animationPhase === index ? 'scale-150 shadow-lg' : ''\n                }`}\n                style={{\n                  backgroundColor: animationPhase === index ? layer.color : '#d1d5db'\n                }}\n              ></div>\n              <span className={`text-sm font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? `Layer ${index + 1}` : `الطبقة ${index + 1}`}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAyB,IAAI;YAAgB;YACzD,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAAsC;YACnH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAkD,IAAI;YAA4C;YACrH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAgD,IAAI;YAA+B;YACtG,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA2B,IAAI;YAAoB;YAC/D,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAA4C;YACzH,OAAO;YACP,MAAM;QACR;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,kBAAkB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;QACzC,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,MAAM,EAAE;QACzB,aAAa,MAAM,EAAE;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;gBAAE,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,mCAAmC;;;;;;0BAG1D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;4BAClB,MAAM,WAAW,mBAAmB;4BACpC,MAAM,aAAa,kBAAkB,MAAM,EAAE;4BAC7C,MAAM,SAAS,OAAO,MAAM,GAAG;4BAC/B,MAAM,aAAa,QAAQ,CAAC;4BAC5B,MAAM,aAAa,QAAQ;4BAE3B,qBACE,8OAAC;gCAEC,WAAW,CAAC,4EAA4E,EACtF,aAAa,cAAc,mBAC3B;gCACF,OAAO;oCACL,KAAK,GAAG,KAAK,QAAQ,GAAG,EAAE,CAAC;oCAC3B,QAAQ;oCACR,QAAQ;oCACR,WAAW,CAAC,WAAW,EAAE,WAAW,eAAe,EAAE,WAAW,kBAAkB,CAAC;oCACnF,gBAAgB;gCAClB;gCACA,SAAS,IAAM,iBAAiB;;kDAGhC,8OAAC;wCACC,WAAW,CAAC,iFAAiF,EAC3F,WAAW,4BAA4B,kBACxC,CAAC,EAAE,aAAa,4BAA4B,IAAI;wCACjD,OAAO;4CACL,iBAAiB,MAAM,KAAK;4CAC5B,YAAY,CAAC,wBAAwB,EAAE,MAAM,KAAK,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,QAAQ,CAAC;wCACjF;;0DAGA,8OAAC;gDAAI,WAAW,CAAC,8BAA8B,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kEACpG,8OAAC;wDAAI,WAAW,CAAC,OAAO,EAAE,aAAa,OAAO,eAAe,aAAa;;0EACxE,8OAAC;gEAAG,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,gBAAgB,IAAI;0EACzF,MAAM,IAAI,CAAC,SAAS;;;;;;0EAEvB,8OAAC;gEAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;0EAC5E,MAAM,WAAW,CAAC,SAAS;;;;;;;;;;;;kEAKhC,8OAAC;wDAAI,WAAW,CAAC,uFAAuF,EAAE,aAAa,OAAO,SAAS,QAAQ;kEAC5I,QAAQ;;;;;;;;;;;;4CAKZ,0BACC,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAKnB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,MAAM,KAAK;4CAC5B,WAAW;4CACX,QAAQ,CAAC;4CACT,QAAQ;wCACV;;;;;;;+BAtDG,MAAM,EAAE;;;;;wBA0DnB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAW,CAAC,eAAe,EAAE,aAAa,OAAO,WAAW,UAAU,gDAAgD,EAAE,aAAa,OAAO,SAAS,QAAQ;kCAC/J,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;gCAEC,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAErF,8OAAC;wCACC,WAAW,CAAC,iDAAiD,EAC3D,mBAAmB,QAAQ,wBAAwB,IACnD;wCACF,OAAO;4CACL,iBAAiB,mBAAmB,QAAQ,MAAM,KAAK,GAAG;wCAC5D;;;;;;kDAEF,8OAAC;wCAAK,WAAW,CAAC,oBAAoB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC/H,aAAa,OAAO,CAAC,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,QAAQ,GAAG;;;;;;;+BAZ9D;;;;;;;;;;;;;;;;;;;;;;AAoBnB", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\nimport DAMAWheel from '../../../components/DAMAWheel';\nimport EALayers from '../../../components/EALayers';\nimport SpecificationView from '../../../components/SpecificationView';\n\ninterface Framework {\n  id: string;\n  name: string;\n  country: 'saudi' | 'qatar';\n  type: 'data-management' | 'enterprise-architecture';\n}\n\nconst frameworks: Framework[] = [\n  { id: 'ndmo', name: 'NDMO', country: 'saudi', type: 'data-management' },\n  { id: 'npc', name: 'NPC', country: 'qatar', type: 'data-management' },\n  { id: 'noura', name: 'NOURA', country: 'saudi', type: 'enterprise-architecture' },\n  { id: 'gea', name: 'GEA', country: 'qatar', type: 'enterprise-architecture' }\n];\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedCategory, setSelectedCategory] = useState<'main' | 'data-management' | 'enterprise-architecture'>('main');\n  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);\n  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={frameworkIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        {selectedCategory === 'main' && (\n          <div className=\"px-12 py-20\">\n            {/* Two Larger Enhanced Cards */}\n            <div className={`flex gap-16 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n\n              {/* Data Management Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('data-management')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 right-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 left-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Data Management' : 'إدارة البيانات'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Standards' : 'الأطر والمعايير'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Enterprise Architecture Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('enterprise-architecture')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 left-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 right-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Models' : 'الأطر والنماذج'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Data Management Frameworks */}\n        {selectedCategory === 'data-management' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Data Management Frameworks' : 'أطر إدارة البيانات'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NDMO Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('ndmo')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NDMO\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Data Management Office' : 'مكتب إدارة البيانات الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* NPC Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('npc')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NPC\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Planning Council' : 'مجلس التخطيط الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enterprise Architecture Frameworks */}\n        {selectedCategory === 'enterprise-architecture' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Enterprise Architecture Frameworks' : 'أطر هندسة المؤسسة'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NOURA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('noura')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NOURA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Enterprise Architecture' : 'هندسة المؤسسة الوطنية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* GEA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('gea')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      GEA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Government Enterprise Architecture' : 'هندسة المؤسسة الحكومية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Framework Detail Views */}\n        {selectedFramework && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedFramework(null)}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {selectedFramework.toUpperCase()}\n              </h2>\n\n              {/* Country Tag */}\n              <div className={`px-3 py-1 rounded-full text-white text-sm font-bold flex items-center gap-2 ${language === 'ar' ? 'mr-4' : 'ml-4'}`}\n                style={{\n                  backgroundColor: frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '#16a34a' : '#dc2626'\n                }}\n              >\n                <span>{frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '🇸🇦' : '🇶🇦'}</span>\n                <span>\n                  {frameworks.find(f => f.id === selectedFramework)?.country === 'saudi'\n                    ? (language === 'en' ? 'Saudi Arabia' : 'السعودية')\n                    : (language === 'en' ? 'Qatar' : 'قطر')\n                  }\n                </span>\n              </div>\n            </div>\n\n            {/* DAMA Wheel for Data Management Frameworks */}\n            {(selectedFramework === 'ndmo' || selectedFramework === 'npc') && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <DAMAWheel\n                  language={language}\n                  onDomainClick={(domain) => {\n                    console.log(`Clicked domain: ${domain} in framework: ${selectedFramework}`);\n                  }}\n                />\n              </div>\n            )}\n\n            {/* EA Layers for Enterprise Architecture Frameworks */}\n            {(selectedFramework === 'noura' || selectedFramework === 'gea') && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <EALayers\n                  language={language}\n                  onLayerClick={(layer) => {\n                    console.log(`Clicked layer: ${layer} in framework: ${selectedFramework}`);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAeA,MAAM,aAA0B;IAC9B;QAAE,IAAI;QAAQ,MAAM;QAAQ,SAAS;QAAS,MAAM;IAAkB;IACtE;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAAkB;IACpE;QAAE,IAAI;QAAS,MAAM;QAAS,SAAS;QAAS,MAAM;IAA0B;IAChF;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAA0B;CAC7E;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0D;IACjH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,8OAAC;gBAAI,WAAU;;oBACZ,qBAAqB,wBACpB,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;8CAGlF,8OAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAwF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACrI,8OAAC;wDAAI,WAAU;wDAAiF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIhI,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC1F,aAAa,OAAO,oBAAoB;;;;;;kEAE3C,8OAAC;wDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC5E,aAAa,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;8CAOxD,8OAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAyF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACtI,8OAAC;wDAAI,WAAU;wDAAkF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIjI,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC1F,aAAa,OAAO,4BAA4B;;;;;;kEAEnD,8OAAC;wDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC5E,aAAa,OAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU1D,qBAAqB,mCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7F,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,8OAAC;wCAAG,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,+BAA+B;;;;;;;;;;;;0CAIxD,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAElF,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;kDAOjE,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUhE,qBAAqB,2CACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7F,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,8OAAC;wCAAG,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,uCAAuC;;;;;;;;;;;;0CAIhE,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAElF,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;kDAOlE,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUzE,mCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7F,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,8OAAC;wCAAG,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,kBAAkB,WAAW;;;;;;kDAIhC,8OAAC;wCAAI,WAAW,CAAC,4EAA4E,EAAE,aAAa,OAAO,SAAS,QAAQ;wCAClI,OAAO;4CACL,iBAAiB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAAoB,YAAY,UAAU,YAAY;wCACvG;;0DAEA,8OAAC;0DAAM,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAAoB,YAAY,UAAU,SAAS;;;;;;0DACxF,8OAAC;0DACE,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAAoB,YAAY,UAC1D,aAAa,OAAO,iBAAiB,aACrC,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;;4BAOxC,CAAC,sBAAsB,UAAU,sBAAsB,KAAK,mBAC3D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,+HAAA,CAAA,UAAS;oCACR,UAAU;oCACV,eAAe,CAAC;wCACd,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,eAAe,EAAE,mBAAmB;oCAC5E;;;;;;;;;;;4BAML,CAAC,sBAAsB,WAAW,sBAAsB,KAAK,mBAC5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8HAAA,CAAA,UAAQ;oCACP,UAAU;oCACV,cAAc,CAAC;wCACb,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,eAAe,EAAE,mBAAmB;oCAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}]}