{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,eAAe,aAAa;QACvF,OAAO;YACL,YAAY;QACd;;0BAIA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,OAAO,YAAY,UAAU;;;;;;kCAC9G,8OAAC;wBAAI,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,YAAY,YAAY;;;;;;kCAC5H,8OAAC;wBAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,OAAO,aAAa,aAAa;;;;;;;;;;;;0BAItH,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAW,CAAC,kDAAkD,EAAE,aAAa,OAAO,qCAAqC,IAAI;sCAC9H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,8OAAC;4CACC,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;4CACtE,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;4BAEvF,sBACC,8OAAC;gCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,SAAS,QAAQ;0CACpE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAE,WAAW,CAAC,gEAAgE,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtH;;;;;;kDAKL,8OAAC;wCAAG,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACpH;;;;;;oCAIF,6BACC,8OAAC;wCAAE,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtG;;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport <PERSON> from '../../../components/Hero';\n\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n  role: 'admin' | 'consultant' | 'project-manager' | 'trainee';\n  status: 'active' | 'inactive';\n  createdAt: string;\n  lastLogin: string;\n}\n\ninterface UserModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (user: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => void;\n  user?: User | null;\n  language: 'en' | 'ar';\n}\n\nfunction UserModal({ isOpen, onClose, onSave, user, language }: UserModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    role: 'trainee' as User['role'],\n    status: 'active' as User['status']\n  });\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        role: user.role,\n        status: user.status\n      });\n    } else {\n      setFormData({\n        name: '',\n        email: '',\n        role: 'trainee',\n        status: 'active'\n      });\n    }\n  }, [user, isOpen]);\n\n  const content = {\n    en: {\n      addUser: 'Add New User',\n      editUser: 'Edit User',\n      name: 'Full Name',\n      email: 'Email Address',\n      role: 'Role',\n      status: 'Status',\n      active: 'Active',\n      inactive: 'Inactive',\n      admin: 'Admin',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      trainee: 'Trainee',\n      save: 'Save',\n      cancel: 'Cancel'\n    },\n    ar: {\n      addUser: 'إضافة مستخدم جديد',\n      editUser: 'تعديل المستخدم',\n      name: 'الاسم الكامل',\n      email: 'عنوان البريد الإلكتروني',\n      role: 'الدور',\n      status: 'الحالة',\n      active: 'نشط',\n      inactive: 'غير نشط',\n      admin: 'مدير',\n      consultant: 'مستشار',\n      projectManager: 'مدير مشروع',\n      trainee: 'متدرب',\n      save: 'حفظ',\n      cancel: 'إلغاء'\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(formData);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\" dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      <div className=\"bg-white rounded-2xl p-8 w-full max-w-md mx-4 shadow-2xl\">\n        <h2 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {user ? content[language].editUser : content[language].addUser}\n        </h2>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Name Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].name}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              required\n            />\n          </div>\n\n          {/* Email Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].email}\n            </label>\n            <input\n              type=\"email\"\n              value={formData.email}\n              onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              required\n            />\n          </div>\n\n          {/* Role Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].role}\n            </label>\n            <select\n              value={formData.role}\n              onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'] })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n            >\n              <option value=\"trainee\">{content[language].trainee}</option>\n              <option value=\"consultant\">{content[language].consultant}</option>\n              <option value=\"project-manager\">{content[language].projectManager}</option>\n              <option value=\"admin\">{content[language].admin}</option>\n            </select>\n          </div>\n\n          {/* Status Field */}\n          <div>\n            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].status}\n            </label>\n            <select\n              value={formData.status}\n              onChange={(e) => setFormData({ ...formData, status: e.target.value as User['status'] })}\n              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n            >\n              <option value=\"active\">{content[language].active}</option>\n              <option value=\"inactive\">{content[language].inactive}</option>\n            </select>\n          </div>\n\n          {/* Buttons */}\n          <div className={`flex gap-4 pt-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n            <button\n              type=\"submit\"\n              className={`flex-1 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              {content[language].save}\n            </button>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className={`flex-1 py-3 rounded-xl border border-gray-300 font-semibold transition-colors hover:bg-gray-50 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ color: 'var(--charcoal-grey)' }}\n            >\n              {content[language].cancel}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default function UserManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [users, setUsers] = useState<User[]>([]);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data initialization\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n\n    // Initialize with mock users\n    const mockUsers: User[] = [\n      {\n        id: '1',\n        name: 'Ahmed Al-Rashid',\n        email: '<EMAIL>',\n        role: 'admin',\n        status: 'active',\n        createdAt: '2024-01-15',\n        lastLogin: '2024-08-04'\n      },\n      {\n        id: '2',\n        name: 'Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active',\n        createdAt: '2024-02-20',\n        lastLogin: '2024-08-03'\n      },\n      {\n        id: '3',\n        name: 'Mohammed Hassan',\n        email: '<EMAIL>',\n        role: 'project-manager',\n        status: 'active',\n        createdAt: '2024-03-10',\n        lastLogin: '2024-08-02'\n      },\n      {\n        id: '4',\n        name: 'Emily Chen',\n        email: '<EMAIL>',\n        role: 'trainee',\n        status: 'inactive',\n        createdAt: '2024-04-05',\n        lastLogin: '2024-07-28'\n      },\n      {\n        id: '5',\n        name: 'Omar Abdullah',\n        email: '<EMAIL>',\n        role: 'consultant',\n        status: 'active',\n        createdAt: '2024-05-12',\n        lastLogin: '2024-08-04'\n      }\n    ];\n    setUsers(mockUsers);\n  }, []);\n\n  // CRUD Functions\n  const handleAddUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {\n    const newUser: User = {\n      ...userData,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString().split('T')[0],\n      lastLogin: 'Never'\n    };\n    setUsers([...users, newUser]);\n  };\n\n  const handleEditUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {\n    if (editingUser) {\n      setUsers(users.map(user =>\n        user.id === editingUser.id\n          ? { ...user, ...userData }\n          : user\n      ));\n      setEditingUser(null);\n    }\n  };\n\n  const handleDeleteUser = (userId: string) => {\n    if (confirm(language === 'en' ? 'Are you sure you want to delete this user?' : 'هل أنت متأكد من حذف هذا المستخدم؟')) {\n      setUsers(users.filter(user => user.id !== userId));\n    }\n  };\n\n  const openEditModal = (user: User) => {\n    setEditingUser(user);\n    setIsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setEditingUser(null);\n  };\n\n  // Filter users based on search\n  const filteredUsers = users.filter(user =>\n    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const content = {\n    en: {\n      title: 'User Management',\n      subtitle: 'Administration',\n      description: 'Manage users, roles, and permissions across the platform with comprehensive control and oversight.',\n      addUser: 'Add User',\n      search: 'Search users...',\n      name: 'Name',\n      email: 'Email',\n      role: 'Role',\n      status: 'Status',\n      lastLogin: 'Last Login',\n      actions: 'Actions',\n      edit: 'Edit',\n      delete: 'Delete',\n      active: 'Active',\n      inactive: 'Inactive',\n      admin: 'Admin',\n      consultant: 'Consultant',\n      projectManager: 'Project Manager',\n      trainee: 'Trainee',\n      totalUsers: 'Total Users',\n      activeUsers: 'Active Users',\n      inactiveUsers: 'Inactive Users'\n    },\n    ar: {\n      title: 'إدارة المستخدمين',\n      subtitle: 'الإدارة',\n      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة مع التحكم والإشراف الشامل.',\n      addUser: 'إضافة مستخدم',\n      search: 'البحث عن المستخدمين...',\n      name: 'الاسم',\n      email: 'البريد الإلكتروني',\n      role: 'الدور',\n      status: 'الحالة',\n      lastLogin: 'آخر تسجيل دخول',\n      actions: 'الإجراءات',\n      edit: 'تعديل',\n      delete: 'حذف',\n      active: 'نشط',\n      inactive: 'غير نشط',\n      admin: 'مدير',\n      consultant: 'مستشار',\n      projectManager: 'مدير مشروع',\n      trainee: 'متدرب',\n      totalUsers: 'إجمالي المستخدمين',\n      activeUsers: 'المستخدمون النشطون',\n      inactiveUsers: 'المستخدمون غير النشطين'\n    }\n  };\n\n  const userIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n    </svg>\n  );\n\n  const getRoleLabel = (role: User['role']) => {\n    switch (role) {\n      case 'admin': return content[language].admin;\n      case 'consultant': return content[language].consultant;\n      case 'project-manager': return content[language].projectManager;\n      case 'trainee': return content[language].trainee;\n      default: return role;\n    }\n  };\n\n  const getStatusBadge = (status: User['status']) => {\n    const isActive = status === 'active';\n    return (\n      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${\n        isActive\n          ? 'bg-green-100 text-green-800'\n          : 'bg-red-100 text-red-800'\n      } ${language === 'ar' ? 'font-arabic' : ''}`}>\n        {isActive ? content[language].active : content[language].inactive}\n      </span>\n    );\n  };\n\n  const activeUsers = users.filter(user => user.status === 'active').length;\n  const inactiveUsers = users.filter(user => user.status === 'inactive').length;\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Hero Section */}\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={userIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      {/* Main Content - User Management */}\n      <div className=\"bg-white\">\n        {/* Statistics Cards */}\n        <div className=\"px-12 py-8\">\n          <div className={`flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'} gap-6 mb-8`}>\n            <div className=\"flex-1 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl flex items-center justify-center text-white\" style={{ backgroundColor: 'var(--emerald-green)' }}>\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {users.length}\n                  </p>\n                  <p className={`text-sm font-semibold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].totalUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex-1 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-green-500 flex items-center justify-center text-white\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {activeUsers}\n                  </p>\n                  <p className={`text-sm font-semibold text-green-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].activeUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex-1 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 border border-red-200\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div className=\"w-12 h-12 rounded-xl bg-red-500 flex items-center justify-center text-white\">\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {inactiveUsers}\n                  </p>\n                  <p className={`text-sm font-semibold text-red-700 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {content[language].inactiveUsers}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Controls */}\n          <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n            <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <input\n                type=\"text\"\n                placeholder={content[language].search}\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className={`px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 w-80 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}\n              />\n            </div>\n            <button\n              onClick={() => setIsModalOpen(true)}\n              className={`px-6 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse font-arabic' : 'flex-row'}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              {content[language].addUser}\n            </button>\n          </div>\n\n          {/* Users Table */}\n          <div className=\"bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-lg\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead style={{ backgroundColor: 'var(--emerald-green)' }}>\n                  <tr>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].name}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].email}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].role}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].status}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].lastLogin}\n                    </th>\n                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                      {content[language].actions}\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredUsers.map((user, index) => (\n                    <tr key={user.id} className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        <div className=\"font-semibold\" style={{ color: 'var(--charcoal-grey)' }}>\n                          {user.name}\n                        </div>\n                      </td>\n                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        {user.email}\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        <span className=\"px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800\">\n                          {getRoleLabel(user.role)}\n                        </span>\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                        {getStatusBadge(user.status)}\n                      </td>\n                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>\n                        {user.lastLogin}\n                      </td>\n                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                        <div className={`flex gap-2 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                          <button\n                            onClick={() => openEditModal(user)}\n                            className=\"p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\"\n                            title={content[language].edit}\n                          >\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </button>\n                          <button\n                            onClick={() => handleDeleteUser(user.id)}\n                            className=\"p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors\"\n                            title={content[language].delete}\n                          >\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {filteredUsers.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n                  </svg>\n                </div>\n                <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en' ? 'No users found' : 'لم يتم العثور على مستخدمين'}\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* User Modal */}\n      <UserModal\n        isOpen={isModalOpen}\n        onClose={closeModal}\n        onSave={editingUser ? handleEditUser : handleAddUser}\n        user={editingUser}\n        language={language}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAuBA,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAkB;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,MAAM;YACrB;QACF,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,UAAU;YACV,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA,IAAI;YACF,SAAS;YACT,UAAU;YACV,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;QACP;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;QAA6E,KAAK,aAAa,OAAO,QAAQ;kBAC3H,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,2BAA2B,aAAa;oBAAE,OAAO;wBAAE,OAAO;oBAAuB;8BAC5I,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;8BAGhE,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAAE,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAW,CAAC,qGAAqG,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAC/K,OAAO;wCAAE,mBAAmB;oCAAuB;oCACnD,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAAE,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;8CAE1B,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAW,CAAC,qGAAqG,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAC/K,OAAO;wCAAE,mBAAmB;oCAAuB;oCACnD,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAAE,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,8OAAC;oCACC,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAiB;oCACjF,WAAW,CAAC,qGAAqG,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAC/K,OAAO;wCAAE,mBAAmB;oCAAuB;;sDAEnD,8OAAC;4CAAO,OAAM;sDAAW,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;sDAClD,8OAAC;4CAAO,OAAM;sDAAc,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;sDACxD,8OAAC;4CAAO,OAAM;sDAAmB,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;sDACjE,8OAAC;4CAAO,OAAM;sDAAS,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;;8CACC,8OAAC;oCAAM,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAAE,OAAO;wCAAE,OAAO;oCAAuB;8CACxJ,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;8CAE3B,8OAAC;oCACC,OAAO,SAAS,MAAM;oCACtB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAmB;oCACrF,WAAW,CAAC,qGAAqG,EAAE,aAAa,OAAO,2BAA2B,aAAa;oCAC/K,OAAO;wCAAE,mBAAmB;oCAAuB;;sDAEnD,8OAAC;4CAAO,OAAM;sDAAU,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;sDAChD,8OAAC;4CAAO,OAAM;sDAAY,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;;;;;;;sCAKxD,8OAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;8CACtF,8OAAC;oCACC,MAAK;oCACL,WAAW,CAAC,mFAAmF,EAAE,aAAa,OAAO,gBAAgB,IAAI;oCACzI,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEhD,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;8CAEzB,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,CAAC,+FAA+F,EAAE,aAAa,OAAO,gBAAgB,IAAI;oCACrJ,OAAO;wCAAE,OAAO;oCAAuB;8CAEtC,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,YAAoB;YACxB;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;SACD;QACD,SAAS;IACX,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAgB;YACpB,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD,WAAW;QACb;QACA,SAAS;eAAI;YAAO;SAAQ;IAC9B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa;YACf,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC,IACvB;YAEN,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,aAAa,OAAO,+CAA+C,sCAAsC;YACnH,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC5C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG1D,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,eAAe;QACjB;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,SAAS;YACT,QAAQ;YACR,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,YAAY;YACZ,gBAAgB;YAChB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,eAAe;QACjB;IACF;IAEA,MAAM,yBACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;YAC5C,KAAK;gBAAc,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU;YACtD,KAAK;gBAAmB,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YAC/D,KAAK;gBAAW,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO;YAChD;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,WAAW;QAC5B,qBACE,8OAAC;YAAK,WAAW,CAAC,6CAA6C,EAC7D,WACI,gCACA,0BACL,CAAC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sBACzC,WAAW,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;IAGvE;IAEA,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IACzE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IAE7E,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BAEjE,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAIH,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,CAAC,KAAK,EAAE,aAAa,OAAO,qBAAqB,WAAW,WAAW,CAAC;;8CACtF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0DACxF,8OAAC;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,iBAAiB;gDAAuB;0DACjI,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;kEAC3E,8OAAC;wDAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wDAAE,OAAO;4DAAE,OAAO;wDAAuB;kEAClH,MAAM,MAAM;;;;;;kEAEf,8OAAC;wDAAE,WAAW,CAAC,uCAAuC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC7F,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0DACxF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;kEAC3E,8OAAC;wDAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wDAAE,OAAO;4DAAE,OAAO;wDAAuB;kEAClH;;;;;;kEAEH,8OAAC;wDAAE,WAAW,CAAC,qCAAqC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC3F,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0DACxF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;kEAC3E,8OAAC;wDAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wDAAE,OAAO;4DAAE,OAAO;wDAAuB;kEAClH;;;;;;kEAEH,8OAAC;wDAAE,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEACzF,OAAO,CAAC,SAAS,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EAAE,aAAa,OAAO,qBAAqB,YAAY;;8CAC7G,8OAAC;oCAAI,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,qBAAqB,YAAY;8CAC9F,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAa,OAAO,CAAC,SAAS,CAAC,MAAM;wCACrC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW,CAAC,iFAAiF,EAAE,aAAa,OAAO,2BAA2B,aAAa;wCAC3J,OAAO;4CAAE,mBAAmB;wCAAuB;;;;;;;;;;;8CAGvD,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAC,yGAAyG,EAAE,aAAa,OAAO,iCAAiC,YAAY;oCACxL,OAAO;wCAAE,iBAAiB;oCAAuB;;sDAEjD,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;sCAK9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,OAAO;oDAAE,iBAAiB;gDAAuB;0DACtD,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sEAEzB,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;sEAE1B,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sEAEzB,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;sEAE3B,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,SAAS;;;;;;sEAE9B,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;sEAC9G,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;0DAIhC,8OAAC;0DACE,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;wDAAiB,WAAW,CAAC,4DAA4D,EAAE,QAAQ,MAAM,IAAI,aAAa,cAAc;;0EACvI,8OAAC;gEAAG,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,2BAA2B,aAAa;0EACtF,cAAA,8OAAC;oEAAI,WAAU;oEAAgB,OAAO;wEAAE,OAAO;oEAAuB;8EACnE,KAAK,IAAI;;;;;;;;;;;0EAGd,8OAAC;gEAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,2BAA2B,aAAa;0EACnG,KAAK,KAAK;;;;;;0EAEb,8OAAC;gEAAG,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,2BAA2B,aAAa;0EACtF,cAAA,8OAAC;oEAAK,WAAU;8EACb,aAAa,KAAK,IAAI;;;;;;;;;;;0EAG3B,8OAAC;gEAAG,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,eAAe,aAAa;0EACzE,eAAe,KAAK,MAAM;;;;;;0EAE7B,8OAAC;gEAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,2BAA2B,aAAa;0EACnG,KAAK,SAAS;;;;;;0EAEjB,8OAAC;gEAAG,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,eAAe,aAAa;0EAC1E,cAAA,8OAAC;oEAAI,WAAW,CAAC,WAAW,EAAE,aAAa,OAAO,qBAAqB,YAAY;;sFACjF,8OAAC;4EACC,SAAS,IAAM,cAAc;4EAC7B,WAAU;4EACV,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;sFAE7B,cAAA,8OAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,8OAAC;4EACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4EACvC,WAAU;4EACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;sFAE/B,cAAA,8OAAC;gFAAI,WAAU;gFAAU,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACjE,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDArCtE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;gCAgDvB,cAAc,MAAM,KAAK,mBACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAE,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDACpE,aAAa,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,8OAAC;gBACC,QAAQ;gBACR,SAAS;gBACT,QAAQ,cAAc,iBAAiB;gBACvC,MAAM;gBACN,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}