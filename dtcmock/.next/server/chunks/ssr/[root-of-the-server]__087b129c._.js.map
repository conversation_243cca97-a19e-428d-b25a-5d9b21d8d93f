{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function LoginPage() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedRole, setSelectedRole] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n\n  const roles = [\n    { value: 'admin', label: { en: 'Admin', ar: 'مدير' } },\n    { value: 'consultant', label: { en: 'Consultant', ar: 'مستشار' } },\n    { value: 'project-manager', label: { en: 'Project Manager', ar: 'مدير مشروع' } },\n    { value: 'trainee', label: { en: 'Trainee', ar: 'متدرب' } }\n  ];\n\n  const content = {\n    en: {\n      title: 'Welcome Back',\n      subtitle: 'Sign in to your account',\n      email: 'Email Address',\n      password: 'Password',\n      role: 'Select Role',\n      signin: 'Sign In',\n      forgotPassword: 'Forgot Password?',\n      companyName: 'DTC Accelerator',\n      tagline: 'Empowering Digital Transformation'\n    },\n    ar: {\n      title: 'مرحباً بعودتك',\n      subtitle: 'تسجيل الدخول إلى حسابك',\n      email: 'عنوان البريد الإلكتروني',\n      password: 'كلمة المرور',\n      role: 'اختر الدور',\n      signin: 'تسجيل الدخول',\n      forgotPassword: 'نسيت كلمة المرور؟',\n      companyName: 'مسرع التحول الرقمي',\n      tagline: 'تمكين التحول الرقمي'\n    }\n  };\n\n  const handleLogin = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Mock login function - just log the values\n    console.log('Login attempt:', { email, password, role: selectedRole, language });\n    alert(`Login attempt for ${email} as ${selectedRole} in ${language}`);\n  };\n\n  return (\n    <div className={`min-h-screen flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Left Section - 70% Animated Image/Graphics */}\n      <div className=\"w-[70%] relative overflow-hidden\" style={{ backgroundColor: 'var(--emerald-green)' }}>\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-white animate-slide-in-left\">\n            <div className=\"mb-8\">\n              <div className=\"w-32 h-32 mx-auto mb-6 rounded-full bg-white/10 flex items-center justify-center animate-pulse-subtle\">\n                <div className=\"w-16 h-16 rounded-full\" style={{ backgroundColor: 'var(--deep-emerald)' }}></div>\n              </div>\n              <h1 className={`text-4xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].companyName}</h1>\n              <p className={`text-xl opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].tagline}</p>\n            </div>\n\n            {/* Animated geometric shapes - RTL aware positioning */}\n            <div className={`absolute top-20 w-20 h-20 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-20' : 'left-20'}`}></div>\n            <div className={`absolute bottom-32 w-16 h-16 rounded-lg bg-white/10 animate-pulse-subtle ${language === 'ar' ? 'left-32' : 'right-32'}`} style={{ animationDelay: '1s' }}></div>\n            <div className={`absolute top-1/2 w-12 h-12 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-10' : 'left-10'}`} style={{ animationDelay: '2s' }}></div>\n          </div>\n        </div>\n\n        {/* Gradient overlay */}\n        <div className={`absolute inset-0 ${language === 'ar' ? 'bg-gradient-to-bl' : 'bg-gradient-to-br'} from-transparent to-black/20`}></div>\n      </div>\n\n      {/* Right Section - 30% Login Form */}\n      <div className=\"w-[30%] flex items-center justify-center p-8 bg-white\">\n        <div className=\"w-full max-w-md animate-fade-in-up\">\n          {/* Language Toggle */}\n          <div className={`flex mb-8 ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>\n            <button\n              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}\n              className=\"px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n              style={{\n                backgroundColor: 'var(--emerald-green)',\n                color: 'white'\n              }}\n            >\n              {language === 'en' ? 'العربية' : 'English'}\n            </button>\n          </div>\n\n          {/* Login Form */}\n          <div className={`text-center mb-8 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n            <h2 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n              {content[language].title}\n            </h2>\n            <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].subtitle}</p>\n          </div>\n\n          <form onSubmit={handleLogin} className=\"space-y-6\">\n            {/* Email Input */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].email}\n              </label>\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              />\n            </div>\n\n            {/* Password Input */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].password}\n              </label>\n              <input\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              />\n            </div>\n\n            {/* Role Selection */}\n            <div>\n              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {content[language].role}\n              </label>\n              <select\n                value={selectedRole}\n                onChange={(e) => setSelectedRole(e.target.value)}\n                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}\n                style={{\n                  '--tw-ring-color': 'var(--emerald-green)',\n                  borderColor: 'var(--charcoal-grey)',\n                  direction: language === 'ar' ? 'rtl' : 'ltr'\n                } as React.CSSProperties}\n                required\n              >\n                <option value=\"\">{content[language].role}</option>\n                {roles.map((role) => (\n                  <option key={role.value} value={role.value}>\n                    {role.label[language]}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sign In Button */}\n            <button\n              type=\"submit\"\n              className={`w-full py-3 rounded-lg text-white font-medium transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}\n              style={{ backgroundColor: 'var(--emerald-green)' }}\n            >\n              {content[language].signin}\n            </button>\n\n            {/* Forgot Password */}\n            <div className={`${language === 'ar' ? 'text-right' : 'text-center'}`}>\n              <button\n                type=\"button\"\n                className={`text-sm hover:underline ${language === 'ar' ? 'font-arabic' : ''}`}\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                {content[language].forgotPassword}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAS,OAAO;gBAAE,IAAI;gBAAS,IAAI;YAAO;QAAE;QACrD;YAAE,OAAO;YAAc,OAAO;gBAAE,IAAI;gBAAc,IAAI;YAAS;QAAE;QACjE;YAAE,OAAO;YAAmB,OAAO;gBAAE,IAAI;gBAAmB,IAAI;YAAa;QAAE;QAC/E;YAAE,OAAO;YAAW,OAAO;gBAAE,IAAI;gBAAW,IAAI;YAAQ;QAAE;KAC3D;IAED,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,kBAAkB;YAAE;YAAO;YAAU,MAAM;YAAc;QAAS;QAC9E,MAAM,CAAC,kBAAkB,EAAE,MAAM,IAAI,EAAE,aAAa,IAAI,EAAE,UAAU;IACtE;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;QAAE,KAAK,aAAa,OAAO,QAAQ;;0BAE3H,8OAAC;gBAAI,WAAU;gBAAmC,OAAO;oBAAE,iBAAiB;gBAAuB;;kCACjG,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,OAAO;oDAAE,iBAAiB;gDAAsB;;;;;;;;;;;sDAE1F,8OAAC;4CAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAAG,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;sDAClH,8OAAC;4CAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAAG,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;8CAI1G,8OAAC;oCAAI,WAAW,CAAC,uEAAuE,EAAE,aAAa,OAAO,aAAa,WAAW;;;;;;8CACtI,8OAAC;oCAAI,WAAW,CAAC,yEAAyE,EAAE,aAAa,OAAO,YAAY,YAAY;oCAAE,OAAO;wCAAE,gBAAgB;oCAAK;;;;;;8CACxK,8OAAC;oCAAI,WAAW,CAAC,wEAAwE,EAAE,aAAa,OAAO,aAAa,WAAW;oCAAE,OAAO;wCAAE,gBAAgB;oCAAK;;;;;;;;;;;;;;;;;kCAK3K,8OAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,OAAO,sBAAsB,oBAAoB,6BAA6B,CAAC;;;;;;;;;;;;0BAIlI,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,kBAAkB,eAAe;sCAChF,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,aAAa,OAAO,OAAO;gCACtD,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;0CAEC,aAAa,OAAO,YAAY;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,OAAO,eAAe,eAAe;;8CACpF,8OAAC;oCAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oCAAE,OAAO;wCAAE,OAAO;oCAAuB;8CACxH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;8CAE1B,8OAAC;oCAAE,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,gBAAgB,IAAI;8CAAG,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;sCAGtG,8OAAC;4BAAK,UAAU;4BAAa,WAAU;;8CAErC,8OAAC;;sDACC,8OAAC;4CAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;sDAE1B,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAW,CAAC,gHAAgH,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAC1L,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;;;;;;;;;;;8CAKZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;sDAE7B,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAW,CAAC,gHAAgH,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAC1L,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;;;;;;;;;;;8CAKZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAW,CAAC,+BAA+B,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACtJ,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;sDAEzB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAW,CAAC,gHAAgH,EAAE,aAAa,OAAO,2BAA2B,aAAa;4CAC1L,OAAO;gDACL,mBAAmB;gDACnB,aAAa;gDACb,WAAW,aAAa,OAAO,QAAQ;4CACzC;4CACA,QAAQ;;8DAER,8OAAC;oDAAO,OAAM;8DAAI,OAAO,CAAC,SAAS,CAAC,IAAI;;;;;;gDACvC,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wDAAwB,OAAO,KAAK,KAAK;kEACvC,KAAK,KAAK,CAAC,SAAS;uDADV,KAAK,KAAK;;;;;;;;;;;;;;;;;8CAQ7B,8OAAC;oCACC,MAAK;oCACL,WAAW,CAAC,iFAAiF,EAAE,aAAa,OAAO,gBAAgB,IAAI;oCACvI,OAAO;wCAAE,iBAAiB;oCAAuB;8CAEhD,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;8CAI3B,8OAAC;oCAAI,WAAW,GAAG,aAAa,OAAO,eAAe,eAAe;8CACnE,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wCAC9E,OAAO;4CAAE,OAAO;wCAAuB;kDAEtC,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}