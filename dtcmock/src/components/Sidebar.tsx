'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface SidebarProps {
  userRole: 'admin' | 'consultant' | 'project-manager' | 'trainee';
  language: 'en' | 'ar';
  onLanguageChange: (lang: 'en' | 'ar') => void;
}

// Professional SVG Icons
const Icons = {
  home: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
    </svg>
  ),
  users: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
    </svg>
  ),
  framework: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  ),
  projects: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  ),
  training: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  ),
  language: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
    </svg>
  ),
  logout: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
    </svg>
  ),
  menu: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
    </svg>
  ),
  close: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
  )
};

export default function Sidebar({ userRole, language, onLanguageChange }: SidebarProps) {
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const content = {
    en: {
      home: 'Home',
      userManagement: 'User Management',
      frameworkManagement: 'Framework Management',
      projects: 'Projects',
      trainingCourses: 'Training Courses',
      logout: 'Logout',
      profile: 'Profile',
      toggleSidebar: 'Toggle Sidebar'
    },
    ar: {
      home: 'الرئيسية',
      userManagement: 'إدارة المستخدمين',
      frameworkManagement: 'إدارة الإطار',
      projects: 'المشاريع',
      trainingCourses: 'الدورات التدريبية',
      logout: 'تسجيل الخروج',
      profile: 'الملف الشخصي',
      toggleSidebar: 'تبديل الشريط الجانبي'
    }
  };

  const adminMenuItems = [
    { key: 'home', label: content[language].home, href: '/dashboard', icon: Icons.home },
    { key: 'userManagement', label: content[language].userManagement, href: '/dashboard/users', icon: Icons.users },
    { key: 'frameworkManagement', label: content[language].frameworkManagement, href: '/dashboard/frameworks', icon: Icons.framework },
    { key: 'projects', label: content[language].projects, href: '/dashboard/projects', icon: Icons.projects },
    { key: 'trainingCourses', label: content[language].trainingCourses, href: '/dashboard/training', icon: Icons.training }
  ];

  const otherRoleMenuItems = [
    { key: 'home', label: content[language].home, href: '/dashboard', icon: Icons.home }
  ];

  const menuItems = userRole === 'admin' ? adminMenuItems : otherRoleMenuItems;

  const handleLogout = () => {
    // Mock logout - redirect to login
    router.push('/login');
  };

  return (
    <div
      className={`fixed top-0 h-full transition-all duration-300 z-50 ${
        isCollapsed ? 'w-16' : 'w-72'
      } ${language === 'ar' ? 'right-0' : 'left-0'}`}
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      style={{
        background: 'linear-gradient(180deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',
        boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)'
      }}
    >
      {/* Header with Profile */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className={`flex items-center ${language === 'ar' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              {/* Professional Profile Picture */}
              <div className="relative">
                <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
                <p className={`font-semibold text-sm text-white ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {content[language].profile}
                </p>
                <p className={`text-xs text-white/70 ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {userRole.charAt(0).toUpperCase() + userRole.slice(1).replace('-', ' ')}
                </p>
              </div>
            </div>
          )}

          {/* Modern Toggle Button */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg hover:bg-white/10 transition-all duration-200 text-white"
            title={content[language].toggleSidebar}
          >
            {isCollapsed ? Icons.menu : Icons.close}
          </button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-6">
        <ul className="space-y-1">
          {menuItems.map((item) => (
            <li key={item.key}>
              <Link
                href={item.href}
                className={`group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-white/10 hover:translate-x-1 ${
                  language === 'ar' ? 'flex-row-reverse hover:-translate-x-1' : 'flex-row'
                } text-white/90 hover:text-white`}
              >
                <span className="flex-shrink-0">{item.icon}</span>
                {!isCollapsed && (
                  <span
                    className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'} transition-all duration-200`}
                  >
                    {item.label}
                  </span>
                )}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer with Language Toggle and Logout */}
      <div className="p-4 border-t border-white/10 mt-auto">
        {!isCollapsed && (
          <div className="space-y-2">
            {/* Language Toggle */}
            <button
              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}
              className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-white/10 text-white/90 hover:text-white ${
                language === 'ar' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <span className="flex-shrink-0">{Icons.language}</span>
              <span className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'}`}>
                {language === 'en' ? 'العربية' : 'English'}
              </span>
            </button>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-red-500/20 text-red-200 hover:text-red-100 ${
                language === 'ar' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <span className="flex-shrink-0">{Icons.logout}</span>
              <span className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'}`}>
                {content[language].logout}
              </span>
            </button>
          </div>
        )}

        {isCollapsed && (
          <div className="space-y-2 flex flex-col items-center">
            {/* Language Toggle - Collapsed */}
            <button
              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}
              className="p-3 rounded-xl transition-all duration-200 hover:bg-white/10 text-white/90 hover:text-white"
              title={language === 'en' ? 'العربية' : 'English'}
            >
              {Icons.language}
            </button>

            {/* Logout Button - Collapsed */}
            <button
              onClick={handleLogout}
              className="p-3 rounded-xl transition-all duration-200 hover:bg-red-500/20 text-red-200 hover:text-red-100"
              title={content[language].logout}
            >
              {Icons.logout}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
