@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom Business Color Palette */
  --emerald-green: #026c4a;
  --deep-emerald: #0c402e;
  --black: #010101;
  --charcoal-grey: #272626;
  --white: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom animations for professional feel */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .form-input {
  text-align: right;
}

/* Arabic Font Support */
.font-arabic {
  font-family: 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* RTL specific improvements */
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  text-align: right;
  padding-right: 1rem;
  padding-left: 1rem;
}

[dir="rtl"] button {
  text-align: center;
}

/* RTL animations */
[dir="rtl"] .animate-slide-in-left {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* RTL form improvements */
[dir="rtl"] label {
  text-align: right;
  display: block;
}

[dir="rtl"] .text-center {
  text-align: center;
}

/* Sidebar specific styles */
.sidebar-transition {
  transition: margin-left 0.3s ease, margin-right 0.3s ease;
}

/* Responsive sidebar adjustments */
@media (max-width: 768px) {
  .sidebar-mobile {
    transform: translateX(-100%);
  }

  [dir="rtl"] .sidebar-mobile {
    transform: translateX(100%);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}

/* Ensure proper spacing for dashboard content */
.dashboard-content {
  min-height: calc(100vh - 2rem);
}

/* Professional sidebar styles */
.sidebar-gradient {
  background: linear-gradient(180deg, var(--emerald-green) 0%, var(--deep-emerald) 100%);
}

/* Professional hover effects with subtle animations */
.sidebar-nav-item {
  position: relative;
  overflow: hidden;
}

.sidebar-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.sidebar-nav-item:hover::before {
  transform: translateX(0);
}

[dir="rtl"] .sidebar-nav-item::before {
  transform: translateX(100%);
}

[dir="rtl"] .sidebar-nav-item:hover::before {
  transform: translateX(0);
}

/* Smooth transitions for all interactive elements */
.sidebar-button {
  transition: all 0.2s ease-in-out;
}

/* Professional card shadows */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Hide scrollbar for framework domain navigation */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Framework card animations */
@keyframes blade-slide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-blade-slide {
  animation: blade-slide 0.8s ease-out;
}

/* Enhanced hover effects for framework cards */
.framework-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.framework-card:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Gradient text animation */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-text {
  background: linear-gradient(-45deg, var(--emerald-green), var(--deep-emerald), var(--emerald-green));
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Project Management Specific Styles */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(2, 108, 74, 0.1);
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(2, 108, 74, 0.15);
  border-color: var(--emerald-green);
}

/* Progress bar animations */
.progress-bar {
  background: linear-gradient(90deg, var(--emerald-green), var(--deep-emerald));
  transition: width 0.6s ease-in-out;
}

/* Status badge styles with DTC colors */
.status-planning {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-in-progress {
  background-color: rgba(245, 158, 11, 0.1);
  color: #92400e;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-review {
  background-color: rgba(139, 92, 246, 0.1);
  color: #6b21a8;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.status-completed {
  background-color: rgba(2, 108, 74, 0.1);
  color: var(--deep-emerald);
  border: 1px solid rgba(2, 108, 74, 0.2);
}

.status-on-hold {
  background-color: rgba(239, 68, 68, 0.1);
  color: #b91c1c;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Domain badge styles */
.domain-data {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.domain-ea {
  background-color: rgba(2, 108, 74, 0.1);
  color: var(--deep-emerald);
  border: 1px solid rgba(2, 108, 74, 0.2);
}

/* Table enhancements */
.project-table {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.project-table thead {
  background: linear-gradient(135deg, var(--emerald-green), var(--deep-emerald));
}

.project-table tbody tr:hover {
  background-color: rgba(2, 108, 74, 0.02);
}

/* Modal enhancements */
.project-modal {
  backdrop-filter: blur(8px);
  background-color: rgba(0, 0, 0, 0.5);
}

.project-modal-content {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(2, 108, 74, 0.1);
}

/* Form input focus states with DTC colors */
.form-input:focus {
  ring-color: var(--emerald-green);
  border-color: var(--emerald-green);
  box-shadow: 0 0 0 3px rgba(2, 108, 74, 0.1);
}

/* Button hover effects */
.btn-primary {
  background: linear-gradient(135deg, var(--emerald-green), var(--deep-emerald));
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 20px rgba(2, 108, 74, 0.3);
}

/* Statistics cards */
.stats-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}