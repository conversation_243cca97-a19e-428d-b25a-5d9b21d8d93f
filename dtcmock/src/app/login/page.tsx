'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const router = useRouter();
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [selectedRole, setSelectedRole] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const roles = [
    { value: 'admin', label: { en: 'Admin', ar: 'مدير' } },
    { value: 'consultant', label: { en: 'Consultant', ar: 'مستشار' } },
    { value: 'project-manager', label: { en: 'Project Manager', ar: 'مدير مشروع' } },
    { value: 'trainee', label: { en: 'Trainee', ar: 'متدرب' } }
  ];

  const content = {
    en: {
      title: 'Welcome Back',
      subtitle: 'Sign in to your account',
      email: 'Email Address',
      password: 'Password',
      role: 'Select Role',
      signin: 'Sign In',
      forgotPassword: 'Forgot Password?',
      companyName: 'DTC Accelerator',
      tagline: 'Empowering Digital Transformation'
    },
    ar: {
      title: 'مرحباً بعودتك',
      subtitle: 'تسجيل الدخول إلى حسابك',
      email: 'عنوان البريد الإلكتروني',
      password: 'كلمة المرور',
      role: 'اختر الدور',
      signin: 'تسجيل الدخول',
      forgotPassword: 'نسيت كلمة المرور؟',
      companyName: 'مسرع التحول الرقمي',
      tagline: 'تمكين التحول الرقمي'
    }
  };

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();

    // Mock login validation - accept any email/password combination
    if (email && password && selectedRole) {
      // Store user data in localStorage (in real app, this would be handled by auth context)
      localStorage.setItem('userRole', selectedRole);
      localStorage.setItem('language', language);
      localStorage.setItem('userEmail', email);

      // Redirect to dashboard
      router.push('/dashboard');
    } else {
      alert(language === 'en' ? 'Please fill in all fields' : 'يرجى ملء جميع الحقول');
    }
  };

  return (
    <div className={`min-h-screen flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Left Section - 70% Animated Image/Graphics */}
      <div className="w-[70%] relative overflow-hidden" style={{ backgroundColor: 'var(--emerald-green)' }}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white animate-slide-in-left">
            <div className="mb-8">
              <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-white/10 flex items-center justify-center animate-pulse-subtle">
                <div className="w-16 h-16 rounded-full" style={{ backgroundColor: 'var(--deep-emerald)' }}></div>
              </div>
              <h1 className={`text-4xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].companyName}</h1>
              <p className={`text-xl opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].tagline}</p>
            </div>

            {/* Animated geometric shapes - RTL aware positioning */}
            <div className={`absolute top-20 w-20 h-20 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-20' : 'left-20'}`}></div>
            <div className={`absolute bottom-32 w-16 h-16 rounded-lg bg-white/10 animate-pulse-subtle ${language === 'ar' ? 'left-32' : 'right-32'}`} style={{ animationDelay: '1s' }}></div>
            <div className={`absolute top-1/2 w-12 h-12 rounded-full bg-white/5 animate-pulse-subtle ${language === 'ar' ? 'right-10' : 'left-10'}`} style={{ animationDelay: '2s' }}></div>
          </div>
        </div>

        {/* Gradient overlay */}
        <div className={`absolute inset-0 ${language === 'ar' ? 'bg-gradient-to-bl' : 'bg-gradient-to-br'} from-transparent to-black/20`}></div>
      </div>

      {/* Right Section - 30% Login Form */}
      <div className="w-[30%] flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md animate-fade-in-up">
          {/* Language Toggle */}
          <div className={`flex mb-8 ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>
            <button
              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
              className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              style={{
                backgroundColor: 'var(--emerald-green)',
                color: 'white'
              }}
            >
              {language === 'en' ? 'العربية' : 'English'}
            </button>
          </div>

          {/* Login Form */}
          <div className={`text-center mb-8 ${language === 'ar' ? 'text-right' : 'text-center'}`}>
            <h2 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].title}
            </h2>
            <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].subtitle}</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            {/* Email Input */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].email}
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
                style={{
                  '--tw-ring-color': 'var(--emerald-green)',
                  borderColor: 'var(--charcoal-grey)',
                  direction: language === 'ar' ? 'rtl' : 'ltr'
                } as React.CSSProperties}
                required
              />
            </div>

            {/* Password Input */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].password}
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
                style={{
                  '--tw-ring-color': 'var(--emerald-green)',
                  borderColor: 'var(--charcoal-grey)',
                  direction: language === 'ar' ? 'rtl' : 'ltr'
                } as React.CSSProperties}
                required
              />
            </div>

            {/* Role Selection */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].role}
              </label>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 transition-colors form-input ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
                style={{
                  '--tw-ring-color': 'var(--emerald-green)',
                  borderColor: 'var(--charcoal-grey)',
                  direction: language === 'ar' ? 'rtl' : 'ltr'
                } as React.CSSProperties}
                required
              >
                <option value="">{content[language].role}</option>
                {roles.map((role) => (
                  <option key={role.value} value={role.value}>
                    {role.label[language]}
                  </option>
                ))}
              </select>
            </div>

            {/* Sign In Button */}
            <button
              type="submit"
              className={`w-full py-3 rounded-lg text-white font-medium transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}
              style={{ backgroundColor: 'var(--emerald-green)' }}
            >
              {content[language].signin}
            </button>

            {/* Forgot Password */}
            <div className={`${language === 'ar' ? 'text-right' : 'text-center'}`}>
              <button
                type="button"
                className={`text-sm hover:underline ${language === 'ar' ? 'font-arabic' : ''}`}
                style={{ color: 'var(--emerald-green)' }}
              >
                {content[language].forgotPassword}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
