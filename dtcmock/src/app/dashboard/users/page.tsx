'use client';

import { useState, useEffect } from 'react';
import <PERSON> from '../../../components/Hero';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'consultant' | 'project-manager' | 'trainee';
  status: 'active' | 'inactive';
  createdAt: string;
  lastLogin: string;
}

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (user: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => void;
  user?: User | null;
  language: 'en' | 'ar';
}

function UserModal({ isOpen, onClose, onSave, user, language }: UserModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'trainee' as User['role'],
    status: 'active' as User['status']
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status
      });
    } else {
      setFormData({
        name: '',
        email: '',
        role: 'trainee',
        status: 'active'
      });
    }
  }, [user, isOpen]);

  const content = {
    en: {
      addUser: 'Add New User',
      editUser: 'Edit User',
      name: 'Full Name',
      email: 'Email Address',
      role: 'Role',
      status: 'Status',
      active: 'Active',
      inactive: 'Inactive',
      admin: 'Admin',
      consultant: 'Consultant',
      projectManager: 'Project Manager',
      trainee: 'Trainee',
      save: 'Save',
      cancel: 'Cancel'
    },
    ar: {
      addUser: 'إضافة مستخدم جديد',
      editUser: 'تعديل المستخدم',
      name: 'الاسم الكامل',
      email: 'عنوان البريد الإلكتروني',
      role: 'الدور',
      status: 'الحالة',
      active: 'نشط',
      inactive: 'غير نشط',
      admin: 'مدير',
      consultant: 'مستشار',
      projectManager: 'مدير مشروع',
      trainee: 'متدرب',
      save: 'حفظ',
      cancel: 'إلغاء'
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="bg-white rounded-2xl p-8 w-full max-w-md mx-4 shadow-2xl">
        <h2 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
          {user ? content[language].editUser : content[language].addUser}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Field */}
          <div>
            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].name}
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}
              required
            />
          </div>

          {/* Email Field */}
          <div>
            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].email}
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}
              required
            />
          </div>

          {/* Role Field */}
          <div>
            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].role}
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value as User['role'] })}
              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}
            >
              <option value="trainee">{content[language].trainee}</option>
              <option value="consultant">{content[language].consultant}</option>
              <option value="project-manager">{content[language].projectManager}</option>
              <option value="admin">{content[language].admin}</option>
            </select>
          </div>

          {/* Status Field */}
          <div>
            <label className={`block text-sm font-semibold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].status}
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as User['status'] })}
              className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 transition-colors ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
              style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}
            >
              <option value="active">{content[language].active}</option>
              <option value="inactive">{content[language].inactive}</option>
            </select>
          </div>

          {/* Buttons */}
          <div className={`flex gap-4 pt-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            <button
              type="submit"
              className={`flex-1 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 ${language === 'ar' ? 'font-arabic' : ''}`}
              style={{ backgroundColor: 'var(--emerald-green)' }}
            >
              {content[language].save}
            </button>
            <button
              type="button"
              onClick={onClose}
              className={`flex-1 py-3 rounded-xl border border-gray-300 font-semibold transition-colors hover:bg-gray-50 ${language === 'ar' ? 'font-arabic' : ''}`}
              style={{ color: 'var(--charcoal-grey)' }}
            >
              {content[language].cancel}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function UserManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [users, setUsers] = useState<User[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data initialization
  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);

    // Initialize with mock users
    const mockUsers: User[] = [
      {
        id: '1',
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        createdAt: '2024-01-15',
        lastLogin: '2024-08-04'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'consultant',
        status: 'active',
        createdAt: '2024-02-20',
        lastLogin: '2024-08-03'
      },
      {
        id: '3',
        name: 'Mohammed Hassan',
        email: '<EMAIL>',
        role: 'project-manager',
        status: 'active',
        createdAt: '2024-03-10',
        lastLogin: '2024-08-02'
      },
      {
        id: '4',
        name: 'Emily Chen',
        email: '<EMAIL>',
        role: 'trainee',
        status: 'inactive',
        createdAt: '2024-04-05',
        lastLogin: '2024-07-28'
      },
      {
        id: '5',
        name: 'Omar Abdullah',
        email: '<EMAIL>',
        role: 'consultant',
        status: 'active',
        createdAt: '2024-05-12',
        lastLogin: '2024-08-04'
      }
    ];
    setUsers(mockUsers);
  }, []);

  // CRUD Functions
  const handleAddUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {
    const newUser: User = {
      ...userData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0],
      lastLogin: 'Never'
    };
    setUsers([...users, newUser]);
  };

  const handleEditUser = (userData: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => {
    if (editingUser) {
      setUsers(users.map(user =>
        user.id === editingUser.id
          ? { ...user, ...userData }
          : user
      ));
      setEditingUser(null);
    }
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm(language === 'en' ? 'Are you sure you want to delete this user?' : 'هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(users.filter(user => user.id !== userId));
    }
  };

  const openEditModal = (user: User) => {
    setEditingUser(user);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingUser(null);
  };

  // Filter users based on search
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const content = {
    en: {
      title: 'User Management',
      subtitle: 'Administration',
      description: 'Manage users, roles, and permissions across the platform with comprehensive control and oversight.',
      addUser: 'Add User',
      search: 'Search users...',
      name: 'Name',
      email: 'Email',
      role: 'Role',
      status: 'Status',
      lastLogin: 'Last Login',
      actions: 'Actions',
      edit: 'Edit',
      delete: 'Delete',
      active: 'Active',
      inactive: 'Inactive',
      admin: 'Admin',
      consultant: 'Consultant',
      projectManager: 'Project Manager',
      trainee: 'Trainee',
      totalUsers: 'Total Users',
      activeUsers: 'Active Users',
      inactiveUsers: 'Inactive Users'
    },
    ar: {
      title: 'إدارة المستخدمين',
      subtitle: 'الإدارة',
      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة مع التحكم والإشراف الشامل.',
      addUser: 'إضافة مستخدم',
      search: 'البحث عن المستخدمين...',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      role: 'الدور',
      status: 'الحالة',
      lastLogin: 'آخر تسجيل دخول',
      actions: 'الإجراءات',
      edit: 'تعديل',
      delete: 'حذف',
      active: 'نشط',
      inactive: 'غير نشط',
      admin: 'مدير',
      consultant: 'مستشار',
      projectManager: 'مدير مشروع',
      trainee: 'متدرب',
      totalUsers: 'إجمالي المستخدمين',
      activeUsers: 'المستخدمون النشطون',
      inactiveUsers: 'المستخدمون غير النشطين'
    }
  };

  const userIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
    </svg>
  );

  const getRoleLabel = (role: User['role']) => {
    switch (role) {
      case 'admin': return content[language].admin;
      case 'consultant': return content[language].consultant;
      case 'project-manager': return content[language].projectManager;
      case 'trainee': return content[language].trainee;
      default: return role;
    }
  };

  const getStatusBadge = (status: User['status']) => {
    const isActive = status === 'active';
    return (
      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
        isActive
          ? 'bg-green-100 text-green-800'
          : 'bg-red-100 text-red-800'
      } ${language === 'ar' ? 'font-arabic' : ''}`}>
        {isActive ? content[language].active : content[language].inactive}
      </span>
    );
  };

  const activeUsers = users.filter(user => user.status === 'active').length;
  const inactiveUsers = users.filter(user => user.status === 'inactive').length;

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      {/* Hero Section */}
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={userIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      {/* Main Content - User Management */}
      <div className="bg-white">
        {/* Statistics Cards */}
        <div className="px-12 py-8">
          <div className={`flex ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'} gap-6 mb-8`}>
            <div className="flex-1 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl flex items-center justify-center text-white" style={{ backgroundColor: 'var(--emerald-green)' }}>
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {users.length}
                  </p>
                  <p className={`text-sm font-semibold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].totalUsers}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex-1 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-green-500 flex items-center justify-center text-white">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {activeUsers}
                  </p>
                  <p className={`text-sm font-semibold text-green-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].activeUsers}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex-1 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 border border-red-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className="w-12 h-12 rounded-xl bg-red-500 flex items-center justify-center text-white">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {inactiveUsers}
                  </p>
                  <p className={`text-sm font-semibold text-red-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {content[language].inactiveUsers}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <input
                type="text"
                placeholder={content[language].search}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 w-80 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}
                style={{ '--tw-ring-color': 'var(--emerald-green)' } as React.CSSProperties}
              />
            </div>
            <button
              onClick={() => setIsModalOpen(true)}
              className={`px-6 py-3 rounded-xl text-white font-semibold transition-colors hover:opacity-90 flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse font-arabic' : 'flex-row'}`}
              style={{ backgroundColor: 'var(--emerald-green)' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {content[language].addUser}
            </button>
          </div>

          {/* Users Table */}
          <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-lg">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead style={{ backgroundColor: 'var(--emerald-green)' }}>
                  <tr>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].name}
                    </th>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].email}
                    </th>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].role}
                    </th>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].status}
                    </th>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].lastLogin}
                    </th>
                    <th className={`px-6 py-4 text-white font-semibold ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                      {content[language].actions}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user, index) => (
                    <tr key={user.id} className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                        <div className="font-semibold" style={{ color: 'var(--charcoal-grey)' }}>
                          {user.name}
                        </div>
                      </td>
                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                        {user.email}
                      </td>
                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                        <span className="px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800">
                          {getRoleLabel(user.role)}
                        </span>
                      </td>
                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                        {getStatusBadge(user.status)}
                      </td>
                      <td className={`px-6 py-4 text-gray-600 ${language === 'ar' ? 'text-right font-arabic' : 'text-left'}`}>
                        {user.lastLogin}
                      </td>
                      <td className={`px-6 py-4 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                        <div className={`flex gap-2 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                          <button
                            onClick={() => openEditModal(user)}
                            className="p-2 rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
                            title={content[language].edit}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="p-2 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
                            title={content[language].delete}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                  </svg>
                </div>
                <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {language === 'en' ? 'No users found' : 'لم يتم العثور على مستخدمين'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* User Modal */}
      <UserModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={editingUser ? handleEditUser : handleAddUser}
        user={editingUser}
        language={language}
      />
    </div>
  );
}
