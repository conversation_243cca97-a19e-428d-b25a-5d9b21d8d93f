'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';

export default function TrainingCourses() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Training Courses',
      subtitle: 'Learning & Development',
      description: 'Manage training programs, educational content, and professional development initiatives for organizational growth.',
      placeholder: 'Training course management functionality will be implemented here.'
    },
    ar: {
      title: 'الدورات التدريبية',
      subtitle: 'التعلم والتطوير',
      description: 'إدارة البرامج التدريبية والمحتوى التعليمي ومبادرات التطوير المهني للنمو التنظيمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة الدورات التدريبية هنا.'
    }
  };

  const trainingIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={trainingIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-16 text-center">
          <div
            className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
            style={{ backgroundColor: 'var(--emerald-green)' }}
          >
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
            {language === 'en' ? 'Coming Soon' : 'قريباً'}
          </h2>
          <p className={`text-xl text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
            {content[language].placeholder}
          </p>
        </div>
      </div>
    </div>
  );
}
