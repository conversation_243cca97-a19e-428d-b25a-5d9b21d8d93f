'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Hero from '../../../../components/Hero';

interface Project {
  id: string;
  name: string;
  description: string;
  domain: 'Data' | 'EA';
  country: 'Saudi' | 'Qatar';
  consultant: string;
  projectManager: string;
  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';
  startDate: string;
  endDate: string;
  progress: number;
  createdAt: string;
}

interface OrgNode {
  id: string;
  name: string;
  title: string;
  department: string;
  level: number;
  parentId?: string;
  children?: OrgNode[];
}

interface ClientInfo {
  id: string;
  type: 'Culture' | 'Internal Politics' | 'Business Process' | 'Technology' | 'Stakeholders' | 'Constraints';
  title: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  createdAt: string;
}

interface ProjectTask {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  duration: number;
  progress: number;
  dependencies: string[];
  assignee: string;
  priority: 'High' | 'Medium' | 'Low';
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Blocked';
}

export default function ProjectDetail() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [project, setProject] = useState<Project | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'organization' | 'client-context' | 'project-plan'>('overview');
  const [orgStructure, setOrgStructure] = useState<OrgNode[]>([]);
  const [clientInfo, setClientInfo] = useState<ClientInfo[]>([]);
  const [projectTasks, setProjectTasks] = useState<ProjectTask[]>([]);

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);

    // Mock project data - in real app, fetch by ID
    const mockProjects: Project[] = [
      {
        id: '1',
        name: 'NDMO Data Governance Implementation',
        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'In Progress',
        startDate: '2024-06-01',
        endDate: '2024-12-31',
        progress: 65,
        createdAt: '2024-05-15'
      },
      {
        id: '2',
        name: 'Qatar GEA Enterprise Architecture',
        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',
        domain: 'EA',
        country: 'Qatar',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'Planning',
        startDate: '2024-09-01',
        endDate: '2025-03-31',
        progress: 15,
        createdAt: '2024-08-01'
      },
      {
        id: '3',
        name: 'NPC Coding Standards Rollout',
        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',
        domain: 'Data',
        country: 'Qatar',
        consultant: 'David Wilson',
        projectManager: 'Layla Mansouri',
        status: 'Review',
        startDate: '2024-04-01',
        endDate: '2024-10-31',
        progress: 85,
        createdAt: '2024-03-15'
      },
      {
        id: '4',
        name: 'NORA Infrastructure Modernization',
        description: 'Modernizing national digital infrastructure following NORA framework specifications.',
        domain: 'EA',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'Completed',
        startDate: '2024-01-01',
        endDate: '2024-07-31',
        progress: 100,
        createdAt: '2023-12-01'
      },
      {
        id: '5',
        name: 'Cross-Border Data Integration',
        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'On Hold',
        startDate: '2024-08-01',
        endDate: '2025-02-28',
        progress: 25,
        createdAt: '2024-07-10'
      }
    ];

    const foundProject = mockProjects.find(p => p.id === projectId);
    setProject(foundProject || null);

    // Mock organization structure
    const mockOrgStructure: OrgNode[] = [
      {
        id: '1',
        name: 'Ahmed Al-Rashid',
        title: 'Chief Executive Officer',
        department: 'Executive',
        level: 1,
        children: [
          {
            id: '2',
            name: 'Sarah Johnson',
            title: 'Chief Technology Officer',
            department: 'Technology',
            level: 2,
            parentId: '1',
            children: [
              {
                id: '3',
                name: 'Mohammed Hassan',
                title: 'Senior Project Manager',
                department: 'Technology',
                level: 3,
                parentId: '2'
              },
              {
                id: '4',
                name: 'Emily Chen',
                title: 'Lead Developer',
                department: 'Technology',
                level: 3,
                parentId: '2'
              }
            ]
          },
          {
            id: '5',
            name: 'Omar Abdullah',
            title: 'Chief Operations Officer',
            department: 'Operations',
            level: 2,
            parentId: '1',
            children: [
              {
                id: '6',
                name: 'Fatima Al-Zahra',
                title: 'Operations Manager',
                department: 'Operations',
                level: 3,
                parentId: '5'
              },
              {
                id: '7',
                name: 'David Wilson',
                title: 'Business Analyst',
                department: 'Operations',
                level: 3,
                parentId: '5'
              }
            ]
          }
        ]
      }
    ];
    setOrgStructure(mockOrgStructure);

    // Mock client context information
    const mockClientInfo: ClientInfo[] = [
      {
        id: '1',
        type: 'Culture',
        title: 'Hierarchical Decision Making',
        description: 'The organization follows a traditional hierarchical structure where decisions are made at the top level and cascaded down. This affects project approval processes and change management.',
        priority: 'High',
        createdAt: '2024-08-01'
      },
      {
        id: '2',
        type: 'Internal Politics',
        title: 'Department Silos',
        description: 'Strong departmental boundaries exist with limited cross-functional collaboration. IT and Business units often have conflicting priorities.',
        priority: 'High',
        createdAt: '2024-08-01'
      },
      {
        id: '3',
        type: 'Technology',
        title: 'Legacy System Dependencies',
        description: 'Heavy reliance on legacy systems that are difficult to integrate with modern solutions. This creates technical constraints for the project.',
        priority: 'Medium',
        createdAt: '2024-08-01'
      },
      {
        id: '4',
        type: 'Stakeholders',
        title: 'Multiple Approval Layers',
        description: 'Project decisions require approval from multiple stakeholders across different departments, which can slow down progress.',
        priority: 'Medium',
        createdAt: '2024-08-01'
      },
      {
        id: '5',
        type: 'Business Process',
        title: 'Manual Approval Workflows',
        description: 'Current business processes rely heavily on manual approvals and paper-based documentation, creating inefficiencies.',
        priority: 'Low',
        createdAt: '2024-08-01'
      }
    ];
    setClientInfo(mockClientInfo);

    // Mock project tasks
    const mockProjectTasks: ProjectTask[] = [
      {
        id: '1',
        name: 'Project Initiation',
        description: 'Define project scope, objectives, and initial requirements gathering',
        startDate: '2024-06-01',
        endDate: '2024-06-15',
        duration: 14,
        progress: 100,
        dependencies: [],
        assignee: 'Mohammed Hassan',
        priority: 'High',
        status: 'Completed'
      },
      {
        id: '2',
        name: 'Stakeholder Analysis',
        description: 'Identify and analyze all project stakeholders and their requirements',
        startDate: '2024-06-16',
        endDate: '2024-06-30',
        duration: 14,
        progress: 100,
        dependencies: ['1'],
        assignee: 'Sarah Johnson',
        priority: 'High',
        status: 'Completed'
      },
      {
        id: '3',
        name: 'Current State Assessment',
        description: 'Assess current data governance practices and identify gaps',
        startDate: '2024-07-01',
        endDate: '2024-07-31',
        duration: 30,
        progress: 100,
        dependencies: ['2'],
        assignee: 'David Wilson',
        priority: 'High',
        status: 'Completed'
      }

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'Planning': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Review': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'On Hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {
    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';
  };

  const projectIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={project.name}
        subtitle={content[language].projectDetails}
        description={project.description}
        icon={projectIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Projects' : 'المشاريع', href: '/dashboard/projects' },
          { label: project.name }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-8">
          {/* Back Button */}
          <div className="mb-8">
            <Link
              href="/dashboard/projects"
              className={`inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
            >
              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {content[language].backToProjects}
            </Link>
          </div>

          {/* Project Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Domain Card */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].domain}</p>
                  <p className={`text-lg font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}
                  </p>
                </div>
              </div>
            </div>

            {/* Country Card */}
            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border border-emerald-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <span className="text-lg">{getCountryFlag(project.country)}</span>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].country}</p>
                  <p className={`text-lg font-bold text-emerald-700 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}
                  </p>
                </div>
              </div>
            </div>

            {/* Status Card */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].status}</p>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {project.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Progress Card */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-sm text-orange-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].progress}</p>
                  <p className={`text-lg font-bold text-orange-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{project.progress}%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className={`flex space-x-8 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {[
                { key: 'overview', label: content[language].overview, icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
                { key: 'organization', label: content[language].organization, icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
                { key: 'client-context', label: content[language].clientContext, icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2' },
                { key: 'project-plan', label: content[language].projectPlan, icon: 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-emerald-500 text-emerald-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                >
                  <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
                  </svg>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="min-h-[600px]">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Project Information */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    Project Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].consultant}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.consultant}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].projectManager}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.projectManager}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].startDate}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.startDate}</p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {content[language].endDate}
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>{project.endDate}</p>
                    </div>
                  </div>
                </div>

                {/* Progress Visualization */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    Progress Overview
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className={`text-lg font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        Overall Progress
                      </span>
                      <span className={`text-lg font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>
                        {project.progress}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div
                        className="h-4 rounded-full transition-all duration-300"
                        style={{
                          width: `${project.progress}%`,
                          background: 'linear-gradient(90deg, var(--emerald-green), var(--deep-emerald))'
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'organization' && (
              <div className="space-y-8">
                {/* Organization Structure Header */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      Organization Structure
                    </h3>
                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <button
                        className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                        style={{ backgroundColor: 'var(--emerald-green)' }}
                      >
                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Position
                      </button>
                      <button
                        className={`flex items-center px-4 py-2 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                        style={{ borderColor: 'var(--emerald-green)', color: 'var(--emerald-green)' }}
                      >
                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                        </svg>
                        Import from Excel
                      </button>
                    </div>
                  </div>

                  {/* Organization Chart */}
                  <div className="overflow-x-auto">
                    <div className="min-w-[800px] p-8">
                      {/* CEO Level */}
                      <div className="flex justify-center mb-8">
                        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border-2 border-emerald-200 shadow-lg max-w-xs">
                          <div className="text-center">
                            <div className="w-16 h-16 bg-emerald-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg">
                              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                            <h4 className={`font-bold text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                              Ahmed Al-Rashid
                            </h4>
                            <p className={`text-sm text-emerald-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                              Chief Executive Officer
                            </p>
                            <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                              Executive
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Connection Line */}
                      <div className="flex justify-center mb-8">
                        <div className="w-px h-8 bg-gray-300"></div>
                      </div>

                      {/* Department Heads Level */}
                      <div className="flex justify-center space-x-16 mb-8">
                        {/* CTO */}
                        <div className="flex flex-col items-center">
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border-2 border-blue-200 shadow-lg max-w-xs">
                            <div className="text-center">
                              <div className="w-14 h-14 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg">
                                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                              <h4 className={`font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                Sarah Johnson
                              </h4>
                              <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                Chief Technology Officer
                              </p>
                              <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                Technology
                              </p>
                            </div>
                          </div>

                          {/* CTO's Team */}
                          <div className="mt-8 flex space-x-8">
                            <div className="bg-gradient-to-br from-blue-25 to-blue-50 rounded-xl p-4 border border-blue-100 shadow">
                              <div className="text-center">
                                <div className="w-10 h-10 bg-blue-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white">
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                  Mohammed Hassan
                                </h5>
                                <p className={`text-xs text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                  Senior Project Manager
                                </p>
                              </div>
                            </div>
                            <div className="bg-gradient-to-br from-blue-25 to-blue-50 rounded-xl p-4 border border-blue-100 shadow">
                              <div className="text-center">
                                <div className="w-10 h-10 bg-blue-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white">
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                  Emily Chen
                                </h5>
                                <p className={`text-xs text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                  Lead Developer
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* COO */}
                        <div className="flex flex-col items-center">
                          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border-2 border-purple-200 shadow-lg max-w-xs">
                            <div className="text-center">
                              <div className="w-14 h-14 bg-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white shadow-lg">
                                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                              <h4 className={`font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                Omar Abdullah
                              </h4>
                              <p className={`text-sm text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                Chief Operations Officer
                              </p>
                              <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                Operations
                              </p>
                            </div>
                          </div>

                          {/* COO's Team */}
                          <div className="mt-8 flex space-x-8">
                            <div className="bg-gradient-to-br from-purple-25 to-purple-50 rounded-xl p-4 border border-purple-100 shadow">
                              <div className="text-center">
                                <div className="w-10 h-10 bg-purple-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white">
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                  Fatima Al-Zahra
                                </h5>
                                <p className={`text-xs text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                  Operations Manager
                                </p>
                              </div>
                            </div>
                            <div className="bg-gradient-to-br from-purple-25 to-purple-50 rounded-xl p-4 border border-purple-100 shadow">
                              <div className="text-center">
                                <div className="w-10 h-10 bg-purple-400 rounded-full mx-auto mb-2 flex items-center justify-center text-white">
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                                <h5 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                  David Wilson
                                </h5>
                                <p className={`text-xs text-purple-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                  Business Analyst
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'client-context' && (
              <div className="space-y-8">
                {/* Client Working Domain */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    Client Working Domain
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        Primary Domain
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {project.domain === 'Data' ? 'Data Management & Governance' : 'Enterprise Architecture & Digital Transformation'}
                      </p>
                    </div>
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        Geographic Focus
                      </label>
                      <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {project.country === 'Saudi' ? 'Saudi Arabia - Government Sector' : 'Qatar - Public & Private Sectors'}
                      </p>
                    </div>
                  </div>
                  <div className="mt-6">
                    <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      Domain Description
                    </label>
                    <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {project.domain === 'Data'
                        ? 'The client operates in the data management domain, focusing on establishing comprehensive data governance frameworks, implementing data quality standards, and ensuring compliance with national data regulations. The organization handles sensitive government data and requires robust security measures and privacy controls.'
                        : 'The client specializes in enterprise architecture consulting, helping organizations design and implement strategic technology frameworks. They work closely with government entities to modernize digital infrastructure, establish architectural standards, and drive digital transformation initiatives across various sectors.'
                      }
                    </p>
                  </div>
                </div>

                {/* Client Context Information */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      Client Context Information
                    </h3>
                    <button
                      className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                      style={{ backgroundColor: 'var(--emerald-green)' }}
                    >
                      <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Add Context Info
                    </button>
                  </div>

                  {/* Context Information Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {clientInfo.map((info) => (
                      <div key={info.id} className={`rounded-xl p-6 border-2 shadow-lg transition-all duration-200 hover:shadow-xl hover:-translate-y-1 ${
                        info.type === 'Culture' ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200' :
                        info.type === 'Internal Politics' ? 'bg-gradient-to-br from-red-50 to-red-100 border-red-200' :
                        info.type === 'Technology' ? 'bg-gradient-to-br from-green-50 to-green-100 border-green-200' :
                        info.type === 'Stakeholders' ? 'bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200' :
                        info.type === 'Business Process' ? 'bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200' :
                        'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'
                      }`}>
                        <div className={`flex items-center justify-between mb-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white shadow-lg ${
                            info.type === 'Culture' ? 'bg-blue-500' :
                            info.type === 'Internal Politics' ? 'bg-red-500' :
                            info.type === 'Technology' ? 'bg-green-500' :
                            info.type === 'Stakeholders' ? 'bg-purple-500' :
                            info.type === 'Business Process' ? 'bg-orange-500' :
                            'bg-gray-500'
                          }`}>
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              {info.type === 'Culture' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />}
                              {info.type === 'Internal Politics' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />}
                              {info.type === 'Technology' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />}
                              {info.type === 'Stakeholders' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />}
                              {info.type === 'Business Process' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />}
                              {info.type === 'Constraints' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />}
                            </svg>
                          </div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            info.priority === 'High' ? 'bg-red-100 text-red-800' :
                            info.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          } ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {info.priority}
                          </span>
                        </div>
                        <div>
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 ${
                            info.type === 'Culture' ? 'bg-blue-100 text-blue-800' :
                            info.type === 'Internal Politics' ? 'bg-red-100 text-red-800' :
                            info.type === 'Technology' ? 'bg-green-100 text-green-800' :
                            info.type === 'Stakeholders' ? 'bg-purple-100 text-purple-800' :
                            info.type === 'Business Process' ? 'bg-orange-100 text-orange-800' :
                            'bg-gray-100 text-gray-800'
                          } ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {info.type}
                          </span>
                          <h4 className={`font-bold text-lg mb-3 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                            {info.title}
                          </h4>
                          <p className={`text-gray-700 text-sm leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {info.description}
                          </p>
                        </div>
                        <div className={`flex justify-end mt-4 space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'project-plan' && (
              <div className="space-y-8">
                {/* Project Plan Header */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className={`flex items-center justify-between mb-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                    <h3 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      Project Plan & Timeline
                    </h3>
                    <div className={`flex space-x-4 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <button
                        className={`flex items-center px-4 py-2 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                        style={{ backgroundColor: 'var(--emerald-green)' }}
                      >
                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Task
                      </button>
                      <button
                        className={`flex items-center px-4 py-2 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                        style={{ borderColor: 'var(--emerald-green)', color: 'var(--emerald-green)' }}
                      >
                        <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Export Plan
                      </button>
                    </div>
                  </div>

                  {/* Project Timeline Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-700">{projectTasks.length}</div>
                        <div className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Total Tasks</div>
                      </div>
                    </div>
                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-4 border border-yellow-200">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-700">{projectTasks.filter(t => t.status === 'In Progress').length}</div>
                        <div className={`text-sm text-yellow-600 ${language === 'ar' ? 'font-arabic' : ''}`}>In Progress</div>
                      </div>
                    </div>
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-700">{projectTasks.filter(t => t.status === 'Completed').length}</div>
                        <div className={`text-sm text-green-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Completed</div>
                      </div>
                    </div>
                    <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-700">{projectTasks.filter(t => t.status === 'Blocked').length}</div>
                        <div className={`text-sm text-red-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Blocked</div>
                      </div>
                    </div>
                  </div>

                  {/* Gantt Chart Visualization */}
                  <div className="overflow-x-auto">
                    <div className="min-w-[1200px]">
                      {/* Timeline Header */}
                      <div className="grid grid-cols-12 gap-2 mb-4">
                        <div className="col-span-4 text-sm font-medium text-gray-500 px-4 py-2">Task Details</div>
                        <div className="col-span-8 grid grid-cols-6 gap-1">
                          {['Jun 2024', 'Jul 2024', 'Aug 2024', 'Sep 2024', 'Oct 2024', 'Nov 2024'].map((month) => (
                            <div key={month} className="text-center text-sm font-medium text-gray-500 py-2 border-b border-gray-200">
                              {month}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Task Rows */}
                      <div className="space-y-2">
                        {projectTasks.map((task, index) => (
                          <div key={task.id} className="grid grid-cols-12 gap-2 items-center">
                            {/* Task Info */}
                            <div className="col-span-4 bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                              <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                                <div className="flex-1">
                                  <h4 className={`font-semibold text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                                    {task.name}
                                  </h4>
                                  <p className={`text-xs text-gray-600 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>
                                    {task.assignee}
                                  </p>
                                  <div className={`flex items-center mt-2 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                      task.status === 'Completed' ? 'bg-green-100 text-green-800' :
                                      task.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                                      task.status === 'Blocked' ? 'bg-red-100 text-red-800' :
                                      'bg-gray-100 text-gray-800'
                                    } ${language === 'ar' ? 'font-arabic' : ''}`}>
                                      {task.status}
                                    </span>
                                    <span className={`text-xs text-gray-500 ${language === 'ar' ? 'mr-2' : 'ml-2'} ${language === 'ar' ? 'font-arabic' : ''}`}>
                                      {task.duration}d
                                    </span>
                                  </div>
                                </div>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  task.priority === 'High' ? 'bg-red-100 text-red-800' :
                                  task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                } ${language === 'ar' ? 'font-arabic' : ''}`}>
                                  {task.priority}
                                </span>
                              </div>
                            </div>

                            {/* Timeline Bar */}
                            <div className="col-span-8 relative h-12">
                              <div className="absolute inset-0 grid grid-cols-6 gap-1">
                                {/* Timeline background */}
                                {Array.from({ length: 6 }).map((_, monthIndex) => (
                                  <div key={monthIndex} className="border-r border-gray-100 last:border-r-0"></div>
                                ))}
                              </div>

                              {/* Task Bar */}
                              <div
                                className={`absolute top-2 h-8 rounded-lg shadow-sm flex items-center justify-between px-3 ${
                                  task.status === 'Completed' ? 'bg-gradient-to-r from-green-400 to-green-500' :
                                  task.status === 'In Progress' ? 'bg-gradient-to-r from-yellow-400 to-yellow-500' :
                                  task.status === 'Blocked' ? 'bg-gradient-to-r from-red-400 to-red-500' :
                                  'bg-gradient-to-r from-gray-400 to-gray-500'
                                }`}
                                style={{
                                  left: `${(index * 15) % 80}%`,
                                  width: `${Math.min(task.duration * 2, 40)}%`
                                }}
                              >
                                <span className="text-white text-xs font-medium">{task.progress}%</span>
                                {task.dependencies.length > 0 && (
                                  <div className="flex items-center">
                                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                    </svg>
                                  </div>
                                )}
                              </div>

                              {/* Progress Bar */}
                              <div
                                className="absolute top-2 h-8 bg-white bg-opacity-30 rounded-lg"
                                style={{
                                  left: `${(index * 15) % 80}%`,
                                  width: `${Math.min(task.duration * 2, 40) * (task.progress / 100)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Dependencies Visualization */}
                  <div className="mt-8">
                    <h4 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      Task Dependencies
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {projectTasks.filter(task => task.dependencies.length > 0).map((task) => (
                        <div key={task.id} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
                          <div className={`flex items-center mb-3 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                            <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center text-white shadow-lg">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                              </svg>
                            </div>
                            <h5 className={`font-semibold text-sm ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`} style={{ color: 'var(--charcoal-grey)' }}>
                              {task.name}
                            </h5>
                          </div>
                          <div className="space-y-2">
                            <p className={`text-xs text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>Depends on:</p>
                            {task.dependencies.map((depId) => {
                              const depTask = projectTasks.find(t => t.id === depId);
                              return depTask ? (
                                <div key={depId} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                                  <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                                  <span className={`text-xs text-gray-700 ${language === 'ar' ? 'mr-2 font-arabic' : 'ml-2'}`}>
                                    {depTask.name}
                                  </span>
                                </div>
                              ) : null;
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
},
      {
        id: '4',
        name: 'Framework Design',
        description: 'Design the data governance framework based on NDMO standards',
        startDate: '2024-08-01',
        endDate: '2024-09-15',
        duration: 45,
        progress: 80,
        dependencies: ['3'],
        assignee: 'Omar Abdullah',
        priority: 'High',
        status: 'In Progress'
      },
      {
        id: '5',
        name: 'Policy Development',
        description: 'Develop data governance policies and procedures',
        startDate: '2024-09-16',
        endDate: '2024-10-31',
        duration: 45,
        progress: 30,
        dependencies: ['4'],
        assignee: 'Fatima Al-Zahra',
        priority: 'Medium',
        status: 'In Progress'
      },
      {
        id: '6',
        name: 'Implementation Planning',
        description: 'Create detailed implementation plan and timeline',
        startDate: '2024-11-01',
        endDate: '2024-11-30',
        duration: 30,
        progress: 0,
        dependencies: ['5'],
        assignee: 'Mohammed Hassan',
        priority: 'Medium',
        status: 'Not Started'
      },
      {
        id: '7',
        name: 'Training and Rollout',
        description: 'Train staff and rollout the new data governance framework',
        startDate: '2024-12-01',
        endDate: '2024-12-31',
        duration: 30,
        progress: 0,
        dependencies: ['6'],
        assignee: 'Sarah Johnson',
        priority: 'High',
        status: 'Not Started'
      }
    ];
    setProjectTasks(mockProjectTasks);
  }, [projectId]);

  const content = {
    en: {
      projectDetails: 'Project Details',
      backToProjects: 'Back to Projects',
      overview: 'Overview',
      organization: 'Organization Structure',
      clientContext: 'Client Context',
      projectPlan: 'Project Plan',
      projectNotFound: 'Project Not Found',
      projectNotFoundDesc: 'The requested project could not be found.',
      domain: 'Domain',
      country: 'Country',
      consultant: 'Consultant',
      projectManager: 'Project Manager',
      status: 'Status',
      progress: 'Progress',
      startDate: 'Start Date',
      endDate: 'End Date',
      dataManagement: 'Data Management',
      enterpriseArchitecture: 'Enterprise Architecture',
      saudiArabia: 'Saudi Arabia',
      qatar: 'Qatar'
    },
    ar: {
      projectDetails: 'تفاصيل المشروع',
      backToProjects: 'العودة للمشاريع',
      overview: 'نظرة عامة',
      organization: 'الهيكل التنظيمي',
      clientContext: 'سياق العميل',
      projectPlan: 'خطة المشروع',
      projectNotFound: 'المشروع غير موجود',
      projectNotFoundDesc: 'لم يتم العثور على المشروع المطلوب.',
      domain: 'المجال',
      country: 'الدولة',
      consultant: 'المستشار',
      projectManager: 'مدير المشروع',
      status: 'الحالة',
      progress: 'التقدم',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      dataManagement: 'إدارة البيانات',
      enterpriseArchitecture: 'هندسة المؤسسة',
      saudiArabia: 'المملكة العربية السعودية',
      qatar: 'قطر'
    }
  };

  if (!project) {
    return (
      <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
        <div className="bg-white min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div
              className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
              style={{ backgroundColor: 'var(--charcoal-grey)' }}
            >
              <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].projectNotFound}
            </h2>
            <p className={`text-xl text-gray-600 max-w-2xl mx-auto mb-8 ${language === 'ar' ? 'font-arabic' : ''}`}>
              {content[language].projectNotFoundDesc}
            </p>
            <Link
              href="/dashboard/projects"
              className={`inline-flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
              style={{ backgroundColor: 'var(--emerald-green)' }}
            >
              <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {content[language].backToProjects}
            </Link>
          </div>
        </div>
      </div>
    );
  }
