'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Hero from '../../../components/Hero';

interface Project {
  id: string;
  name: string;
  description: string;
  domain: 'Data' | 'EA';
  country: 'Saudi' | 'Qatar';
  consultant: string;
  projectManager: string;
  status: 'Planning' | 'In Progress' | 'Review' | 'Completed' | 'On Hold';
  startDate: string;
  endDate: string;
  progress: number;
  createdAt: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'consultant' | 'project-manager' | 'trainee';
  status: 'active' | 'inactive';
}

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (project: Omit<Project, 'id' | 'createdAt' | 'progress'>) => void;
  project?: Project | null;
  language: 'en' | 'ar';
  consultants: User[];
  projectManagers: User[];
}

function ProjectModal({ isOpen, onClose, onSave, project, language, consultants, projectManagers }: ProjectModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    domain: 'Data' as 'Data' | 'EA',
    country: 'Saudi' as 'Saudi' | 'Qatar',
    consultant: '',
    projectManager: '',
    status: 'Planning' as Project['status'],
    startDate: '',
    endDate: ''
  });

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name,
        description: project.description,
        domain: project.domain,
        country: project.country,
        consultant: project.consultant,
        projectManager: project.projectManager,
        status: project.status,
        startDate: project.startDate,
        endDate: project.endDate
      });
    } else {
      setFormData({
        name: '',
        description: '',
        domain: 'Data',
        country: 'Saudi',
        consultant: '',
        projectManager: '',
        status: 'Planning',
        startDate: '',
        endDate: ''
      });
    }
  }, [project, isOpen]);

  const content = {
    en: {
      title: project ? 'Edit Project' : 'Create New Project',
      projectName: 'Project Name',
      projectDescription: 'Project Description',
      domain: 'Domain',
      country: 'Country',
      consultant: 'Assign Consultant',
      projectManager: 'Assign Project Manager',
      status: 'Status',
      startDate: 'Start Date',
      endDate: 'End Date',
      save: 'Save Project',
      cancel: 'Cancel',
      selectConsultant: 'Select Consultant',
      selectProjectManager: 'Select Project Manager',
      dataManagement: 'Data Management',
      enterpriseArchitecture: 'Enterprise Architecture',
      saudiArabia: 'Saudi Arabia',
      qatar: 'Qatar',
      planning: 'Planning',
      inProgress: 'In Progress',
      review: 'Review',
      completed: 'Completed',
      onHold: 'On Hold'
    },
    ar: {
      title: project ? 'تعديل المشروع' : 'إنشاء مشروع جديد',
      projectName: 'اسم المشروع',
      projectDescription: 'وصف المشروع',
      domain: 'المجال',
      country: 'الدولة',
      consultant: 'تعيين مستشار',
      projectManager: 'تعيين مدير المشروع',
      status: 'الحالة',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      save: 'حفظ المشروع',
      cancel: 'إلغاء',
      selectConsultant: 'اختر مستشار',
      selectProjectManager: 'اختر مدير المشروع',
      dataManagement: 'إدارة البيانات',
      enterpriseArchitecture: 'هندسة المؤسسة',
      saudiArabia: 'المملكة العربية السعودية',
      qatar: 'قطر',
      planning: 'التخطيط',
      inProgress: 'قيد التنفيذ',
      review: 'المراجعة',
      completed: 'مكتمل',
      onHold: 'معلق'
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto ${language === 'ar' ? 'text-right' : 'text-left'}`}>
        <div className="p-8">
          <div className={`flex items-center justify-between mb-8 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
            <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].title}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Name */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].projectName}
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
                required
              />
            </div>

            {/* Project Description */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].projectDescription}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all resize-none ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
                required
              />
            </div>

            {/* Domain and Country Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Domain */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].domain}
                </label>
                <select
                  value={formData.domain}
                  onChange={(e) => setFormData({ ...formData, domain: e.target.value as 'Data' | 'EA' })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                >
                  <option value="Data">{content[language].dataManagement}</option>
                  <option value="EA">{content[language].enterpriseArchitecture}</option>
                </select>
              </div>

              {/* Country */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].country}
                </label>
                <select
                  value={formData.country}
                  onChange={(e) => setFormData({ ...formData, country: e.target.value as 'Saudi' | 'Qatar' })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                >
                  <option value="Saudi">{content[language].saudiArabia}</option>
                  <option value="Qatar">{content[language].qatar}</option>
                </select>
              </div>
            </div>

            {/* Consultant and Project Manager Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Consultant */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].consultant}
                </label>
                <select
                  value={formData.consultant}
                  onChange={(e) => setFormData({ ...formData, consultant: e.target.value })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                  required
                >
                  <option value="">{content[language].selectConsultant}</option>
                  {consultants.map((consultant) => (
                    <option key={consultant.id} value={consultant.name}>
                      {consultant.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Project Manager */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].projectManager}
                </label>
                <select
                  value={formData.projectManager}
                  onChange={(e) => setFormData({ ...formData, projectManager: e.target.value })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                  required
                >
                  <option value="">{content[language].selectProjectManager}</option>
                  {projectManagers.map((pm) => (
                    <option key={pm.id} value={pm.name}>
                      {pm.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Status and Dates Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Status */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].status}
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as Project['status'] })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                >
                  <option value="Planning">{content[language].planning}</option>
                  <option value="In Progress">{content[language].inProgress}</option>
                  <option value="Review">{content[language].review}</option>
                  <option value="Completed">{content[language].completed}</option>
                  <option value="On Hold">{content[language].onHold}</option>
                </select>
              </div>

              {/* Start Date */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].startDate}
                </label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                  required
                />
              </div>

              {/* End Date */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].endDate}
                </label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  style={{ focusRingColor: 'var(--emerald-green)' }}
                  required
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className={`flex gap-4 pt-6 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
              <button
                type="submit"
                className={`flex-1 py-3 px-6 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'font-arabic' : ''}`}
                style={{ backgroundColor: 'var(--emerald-green)' }}
              >
                {content[language].save}
              </button>
              <button
                type="button"
                onClick={onClose}
                className={`flex-1 py-3 px-6 border-2 font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'font-arabic' : ''}`}
                style={{ borderColor: 'var(--charcoal-grey)', color: 'var(--charcoal-grey)' }}
              >
                {content[language].cancel}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function Projects() {
  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [projects, setProjects] = useState<Project[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDomain, setFilterDomain] = useState<'All' | 'Data' | 'EA'>('All');
  const [filterCountry, setFilterCountry] = useState<'All' | 'Saudi' | 'Qatar'>('All');
  const [filterStatus, setFilterStatus] = useState<'All' | Project['status']>('All');

  useEffect(() => {
    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';

    setUserRole(mockUserRole);
    setLanguage(mockLanguage);

    // Initialize mock users
    const mockUsers: User[] = [
      {
        id: '1',
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'consultant',
        status: 'active'
      },
      {
        id: '3',
        name: 'Mohammed Hassan',
        email: '<EMAIL>',
        role: 'project-manager',
        status: 'active'
      },
      {
        id: '4',
        name: 'Emily Chen',
        email: '<EMAIL>',
        role: 'trainee',
        status: 'inactive'
      },
      {
        id: '5',
        name: 'Omar Abdullah',
        email: '<EMAIL>',
        role: 'consultant',
        status: 'active'
      },
      {
        id: '6',
        name: 'Fatima Al-Zahra',
        email: '<EMAIL>',
        role: 'project-manager',
        status: 'active'
      },
      {
        id: '7',
        name: 'David Wilson',
        email: '<EMAIL>',
        role: 'consultant',
        status: 'active'
      },
      {
        id: '8',
        name: 'Layla Mansouri',
        email: '<EMAIL>',
        role: 'project-manager',
        status: 'active'
      }
    ];
    setUsers(mockUsers);

    // Initialize mock projects
    const mockProjects: Project[] = [
      {
        id: '1',
        name: 'NDMO Data Governance Implementation',
        description: 'Implementing comprehensive data governance framework based on NDMO standards for government entities.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'In Progress',
        startDate: '2024-06-01',
        endDate: '2024-12-31',
        progress: 65,
        createdAt: '2024-05-15'
      },
      {
        id: '2',
        name: 'Qatar GEA Enterprise Architecture',
        description: 'Designing and implementing enterprise architecture framework following GEA guidelines for digital transformation.',
        domain: 'EA',
        country: 'Qatar',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'Planning',
        startDate: '2024-09-01',
        endDate: '2025-03-31',
        progress: 15,
        createdAt: '2024-08-01'
      },
      {
        id: '3',
        name: 'NPC Coding Standards Rollout',
        description: 'Implementing NPC coding standards and best practices across development teams in Qatar.',
        domain: 'Data',
        country: 'Qatar',
        consultant: 'David Wilson',
        projectManager: 'Layla Mansouri',
        status: 'Review',
        startDate: '2024-04-01',
        endDate: '2024-10-31',
        progress: 85,
        createdAt: '2024-03-15'
      },
      {
        id: '4',
        name: 'NORA Infrastructure Modernization',
        description: 'Modernizing national digital infrastructure following NORA framework specifications.',
        domain: 'EA',
        country: 'Saudi',
        consultant: 'Sarah Johnson',
        projectManager: 'Mohammed Hassan',
        status: 'Completed',
        startDate: '2024-01-01',
        endDate: '2024-07-31',
        progress: 100,
        createdAt: '2023-12-01'
      },
      {
        id: '5',
        name: 'Cross-Border Data Integration',
        description: 'Establishing secure data integration protocols between Saudi and Qatar government systems.',
        domain: 'Data',
        country: 'Saudi',
        consultant: 'Omar Abdullah',
        projectManager: 'Fatima Al-Zahra',
        status: 'On Hold',
        startDate: '2024-08-01',
        endDate: '2025-02-28',
        progress: 25,
        createdAt: '2024-07-10'
      }
    ];
    setProjects(mockProjects);
  }, []);

  const content = {
    en: {
      title: 'Projects',
      subtitle: 'Project Management',
      description: 'Manage and track digital transformation projects with comprehensive oversight and progress monitoring.',
      createProject: 'Create New Project',
      searchProjects: 'Search projects...',
      allDomains: 'All Domains',
      allCountries: 'All Countries',
      allStatuses: 'All Statuses',
      dataManagement: 'Data Management',
      enterpriseArchitecture: 'Enterprise Architecture',
      saudiArabia: 'Saudi Arabia',
      qatar: 'Qatar',
      planning: 'Planning',
      inProgress: 'In Progress',
      review: 'Review',
      completed: 'Completed',
      onHold: 'On Hold',
      projectName: 'Project Name',
      domain: 'Domain',
      country: 'Country',
      consultant: 'Consultant',
      projectManager: 'Project Manager',
      status: 'Status',
      progress: 'Progress',
      actions: 'Actions',
      edit: 'Edit',
      delete: 'Delete',
      noProjects: 'No projects found',
      noProjectsDesc: 'No projects match your current filters. Try adjusting your search criteria.',
      adminOnly: 'Admin Access Required',
      adminOnlyDesc: 'Only administrators can create and manage projects.',
      totalProjects: 'Total Projects',
      activeProjects: 'Active Projects',
      completedProjects: 'Completed Projects'
    },
    ar: {
      title: 'المشاريع',
      subtitle: 'إدارة المشاريع',
      description: 'إدارة وتتبع مشاريع التحول الرقمي مع الإشراف الشامل ومراقبة التقدم.',
      createProject: 'إنشاء مشروع جديد',
      searchProjects: 'البحث في المشاريع...',
      allDomains: 'جميع المجالات',
      allCountries: 'جميع الدول',
      allStatuses: 'جميع الحالات',
      dataManagement: 'إدارة البيانات',
      enterpriseArchitecture: 'هندسة المؤسسة',
      saudiArabia: 'المملكة العربية السعودية',
      qatar: 'قطر',
      planning: 'التخطيط',
      inProgress: 'قيد التنفيذ',
      review: 'المراجعة',
      completed: 'مكتمل',
      onHold: 'معلق',
      projectName: 'اسم المشروع',
      domain: 'المجال',
      country: 'الدولة',
      consultant: 'المستشار',
      projectManager: 'مدير المشروع',
      status: 'الحالة',
      progress: 'التقدم',
      actions: 'الإجراءات',
      edit: 'تعديل',
      delete: 'حذف',
      noProjects: 'لا توجد مشاريع',
      noProjectsDesc: 'لا توجد مشاريع تطابق المرشحات الحالية. جرب تعديل معايير البحث.',
      adminOnly: 'مطلوب صلاحية المدير',
      adminOnlyDesc: 'يمكن للمديرين فقط إنشاء وإدارة المشاريع.',
      totalProjects: 'إجمالي المشاريع',
      activeProjects: 'المشاريع النشطة',
      completedProjects: 'المشاريع المكتملة'
    }
  };

  // Get consultants and project managers
  const consultants = users.filter(user => user.role === 'consultant' && user.status === 'active');
  const projectManagers = users.filter(user => user.role === 'project-manager' && user.status === 'active');

  // Filter projects based on search and filters
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.consultant.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.projectManager.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDomain = filterDomain === 'All' || project.domain === filterDomain;
    const matchesCountry = filterCountry === 'All' || project.country === filterCountry;
    const matchesStatus = filterStatus === 'All' || project.status === filterStatus;

    return matchesSearch && matchesDomain && matchesCountry && matchesStatus;
  });

  // Project statistics
  const totalProjects = projects.length;
  const activeProjects = projects.filter(p => p.status === 'In Progress' || p.status === 'Planning').length;
  const completedProjects = projects.filter(p => p.status === 'Completed').length;

  // CRUD Functions
  const handleAddProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'progress'>) => {
    const newProject: Project = {
      ...projectData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0],
      progress: 0
    };
    setProjects([...projects, newProject]);
  };

  const handleEditProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'progress'>) => {
    if (editingProject) {
      setProjects(projects.map(project =>
        project.id === editingProject.id
          ? { ...project, ...projectData }
          : project
      ));
      setEditingProject(null);
    }
  };

  const handleDeleteProject = (projectId: string) => {
    if (confirm(language === 'en' ? 'Are you sure you want to delete this project?' : 'هل أنت متأكد من حذف هذا المشروع؟')) {
      setProjects(projects.filter(project => project.id !== projectId));
    }
  };

  const openEditModal = (project: Project) => {
    setEditingProject(project);
    setIsModalOpen(true);
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'Planning': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Review': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'On Hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDomainColor = (domain: 'Data' | 'EA') => {
    return domain === 'Data'
      ? 'bg-blue-50 text-blue-700 border-blue-200'
      : 'bg-emerald-50 text-emerald-700 border-emerald-200';
  };

  const getCountryFlag = (country: 'Saudi' | 'Qatar') => {
    return country === 'Saudi' ? '🇸🇦' : '🇶🇦';
  };

  const projectIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={projectIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-16">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-2xl font-bold text-blue-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{totalProjects}</p>
                  <p className={`text-sm text-blue-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].totalProjects}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 border border-yellow-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-2xl font-bold text-yellow-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{activeProjects}</p>
                  <p className={`text-sm text-yellow-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].activeProjects}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                  <p className={`text-2xl font-bold text-green-700 ${language === 'ar' ? 'font-arabic' : ''}`}>{completedProjects}</p>
                  <p className={`text-sm text-green-600 ${language === 'ar' ? 'font-arabic' : ''}`}>{content[language].completedProjects}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Header with Create Button */}
          <div className={`flex items-center justify-between mb-8 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
            <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
              {content[language].title}
            </h2>
            {userRole === 'admin' && (
              <button
                onClick={() => {
                  setEditingProject(null);
                  setIsModalOpen(true);
                }}
                className={`flex items-center px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1 ${language === 'ar' ? 'flex-row-reverse font-arabic' : ''}`}
                style={{ backgroundColor: 'var(--emerald-green)' }}
              >
                <svg className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                {content[language].createProject}
              </button>
            )}
          </div>

          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            {/* Search */}
            <div className="md:col-span-1">
              <input
                type="text"
                placeholder={content[language].searchProjects}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
              />
            </div>

            {/* Domain Filter */}
            <div>
              <select
                value={filterDomain}
                onChange={(e) => setFilterDomain(e.target.value as 'All' | 'Data' | 'EA')}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
              >
                <option value="All">{content[language].allDomains}</option>
                <option value="Data">{content[language].dataManagement}</option>
                <option value="EA">{content[language].enterpriseArchitecture}</option>
              </select>
            </div>

            {/* Country Filter */}
            <div>
              <select
                value={filterCountry}
                onChange={(e) => setFilterCountry(e.target.value as 'All' | 'Saudi' | 'Qatar')}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
              >
                <option value="All">{content[language].allCountries}</option>
                <option value="Saudi">{content[language].saudiArabia}</option>
                <option value="Qatar">{content[language].qatar}</option>
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as 'All' | Project['status'])}
                className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                style={{ focusRingColor: 'var(--emerald-green)' }}
              >
                <option value="All">{content[language].allStatuses}</option>
                <option value="Planning">{content[language].planning}</option>
                <option value="In Progress">{content[language].inProgress}</option>
                <option value="Review">{content[language].review}</option>
                <option value="Completed">{content[language].completed}</option>
                <option value="On Hold">{content[language].onHold}</option>
              </select>
            </div>
          </div>

          {/* Projects Table or Access Control */}
          {userRole !== 'admin' ? (
            // Non-admin users see access restriction
            <div className="text-center py-16">
              <div
                className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
                style={{ backgroundColor: 'var(--charcoal-grey)' }}
              >
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].adminOnly}
              </h3>
              <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                {content[language].adminOnlyDesc}
              </p>
            </div>
          ) : filteredProjects.length === 0 ? (
            // No projects found
            <div className="text-center py-16">
              <div
                className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
                style={{ backgroundColor: 'var(--emerald-green)' }}
              >
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                {content[language].noProjects}
              </h3>
              <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                {content[language].noProjectsDesc}
              </p>
            </div>
          ) : (
            // Projects Table
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead style={{ backgroundColor: 'var(--emerald-green)' }}>
                    <tr>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].projectName}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].domain}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].country}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].consultant}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].projectManager}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].status}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].progress}
                      </th>
                      <th className={`px-6 py-4 text-left text-sm font-semibold text-white ${language === 'ar' ? 'text-right font-arabic' : ''}`}>
                        {content[language].actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredProjects.map((project, index) => (
                      <tr key={project.id} className={`hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                        <td className="px-6 py-4">
                          <div>
                            <Link
                              href={`/dashboard/projects/${project.id}`}
                              className={`font-semibold hover:underline transition-colors ${language === 'ar' ? 'font-arabic' : ''}`}
                              style={{ color: 'var(--emerald-green)' }}
                            >
                              {project.name}
                            </Link>
                            <div className={`text-sm text-gray-600 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>
                              {project.description.length > 60 ? `${project.description.substring(0, 60)}...` : project.description}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getDomainColor(project.domain)} ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {project.domain === 'Data' ? content[language].dataManagement : content[language].enterpriseArchitecture}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                            <span className={`${language === 'ar' ? 'ml-2' : 'mr-2'}`}>{getCountryFlag(project.country)}</span>
                            <span className={`text-sm font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                              {project.country === 'Saudi' ? content[language].saudiArabia : content[language].qatar}
                            </span>
                          </div>
                        </td>
                        <td className={`px-6 py-4 text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                          {project.consultant}
                        </td>
                        <td className={`px-6 py-4 text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                          {project.projectManager}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)} ${language === 'ar' ? 'font-arabic' : ''}`}>
                            {content[language][project.status.toLowerCase().replace(' ', '') as keyof typeof content['en']]}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${project.progress}%`,
                                  backgroundColor: 'var(--emerald-green)'
                                }}
                              ></div>
                            </div>
                            <span className={`ml-2 text-sm font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                              {project.progress}%
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className={`flex space-x-2 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                            <button
                              onClick={() => openEditModal(project)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                              title={content[language].edit}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleDeleteProject(project.id)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              title={content[language].delete}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Project Modal */}
      <ProjectModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingProject(null);
        }}
        onSave={editingProject ? handleEditProject : handleAddProject}
        project={editingProject}
        language={language}
        consultants={consultants}
        projectManagers={projectManagers}
      />
    </div>
  );
}
