'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';

export default function Projects() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Projects',
      subtitle: 'Project Management',
      description: 'Manage and track digital transformation projects with comprehensive oversight and progress monitoring.',
      placeholder: 'Project management functionality will be implemented here.'
    },
    ar: {
      title: 'المشاريع',
      subtitle: 'إدارة المشاريع',
      description: 'إدارة وتتبع مشاريع التحول الرقمي مع الإشراف الشامل ومراقبة التقدم.',
      placeholder: 'سيتم تنفيذ وظائف إدارة المشاريع هنا.'
    }
  };

  const projectIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={projectIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-16 text-center">
          <div
            className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
            style={{ backgroundColor: 'var(--emerald-green)' }}
          >
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
            {language === 'en' ? 'Coming Soon' : 'قريباً'}
          </h2>
          <p className={`text-xl text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
            {content[language].placeholder}
          </p>
        </div>
      </div>
    </div>
  );
}
